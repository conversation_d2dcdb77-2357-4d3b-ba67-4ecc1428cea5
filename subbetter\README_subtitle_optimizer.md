# 字幕时间优化工具

## 功能简介

这个工具使用FFmpeg的silencedetect滤镜检测音频中的静音区域，然后优化字幕时间戳，剪掉字幕条目前后的静音区域，使字幕与语音精确对齐。

## 核心特性

- **毫秒级精度**: 支持毫秒级的时间戳优化
- **高性能处理**: 单次FFmpeg调用，高效处理大文件
- **智能边界检测**: 只修剪静音区域，保留语音内容
- **字幕扩充**: 自动扩充字幕显示时间，提升阅读体验
- **智能冲突解决**: 自动处理扩充后的时间重叠问题
- **可配置参数**: 支持自定义静音检测阈值和扩充时间
- **详细日志**: 显示每个字幕条目的优化和扩充结果

## 安装要求

- Python 3.6+
- FFmpeg (需要在PATH中可用)

## 使用方法

### 基本用法

```bash
python subtitle_optimizer.py <音频文件> <输入SRT文件> <输出SRT文件>
```

### 示例

```bash
# 基本使用
python subtitle_optimizer.py audio.wav subtitles.srt optimized.srt

# 自定义参数
python subtitle_optimizer.py audio.wav subtitles.srt optimized.srt -n -35 -d 0.005 -e 300

# 禁用扩充功能
python subtitle_optimizer.py audio.wav subtitles.srt optimized.srt -e 0
```

### 参数说明

- `audio_file`: 音频文件路径 (支持wav, mp3, m4a, flac等格式)
- `srt_file`: 输入SRT字幕文件路径
- `output_file`: 输出SRT字幕文件路径
- `-n, --noise-threshold`: 静音阈值 (dB)，默认-30dB
- `-d, --min-duration`: 最小静音持续时间 (秒)，默认0.01秒
- `-e, --expand`: 扩充字幕显示时间 (毫秒)，默认200ms
- `-b, --bridge`: 桥接短间隔阈值 (毫秒)，默认300ms

## 算法原理

### 向内搜索策略

工具采用"向内搜索"的策略来优化字幕时间戳：

1. **前向搜索**: 从字幕开始时间向内搜索
   - 如果开始时间在静音中 → 修剪到静音结束点（语音开始）
   - 如果开始时间已在语音中 → 保持原时间

2. **后向搜索**: 从字幕结束时间向内搜索
   - 如果结束时间在静音中 → 修剪到静音开始点（语音结束）
   - 如果结束时间已在语音中 → 保持原时间

### 扩充功能原理

**扩充策略**：
1. **前向扩充**: 字幕开始时间提前200ms
2. **后向扩充**: 字幕结束时间延后200ms
3. **冲突解决**: 自动处理重叠，在中点分割
4. **边界保护**: 确保开始时间不小于0

**智能冲突解决**：
```
原始: A[10.0-12.0] B[12.1-14.0]
扩充: A[9.8-12.2] B[11.9-14.2]  # 重叠300ms
解决: A[9.8-12.05] B[12.05-14.2] # 中点分割
```

### 桥接功能原理

**桥接策略**：
1. **间隔检测**: 检测相邻字幕间的时间间隔
2. **阈值判断**: 小于300ms的间隔进行桥接
3. **中点分割**: 在间隔中点连接两个字幕
4. **连续显示**: 消除短暂的空白闪烁

**桥接示例**：
```
原始: A[10.0-12.0] 间隔200ms B[12.2-14.0]
桥接: A[10.0-12.1] B[12.1-14.0]  # 消除200ms间隔
效果: 字幕连续显示，无闪烁
```

### 技术流程

```
音频文件 → FFmpeg静音检测 → 解析静音段 → 字幕时间优化 → 扩充处理 → 桥接处理 → 输出SRT文件
```

## 输出示例

```
============================================================
字幕时间优化工具
============================================================
音频文件: audio.wav
字幕文件: subtitles.srt
输出文件: optimized.srt
静音阈值: -30.0dB
最小静音时长: 0.01秒
扩充时间: 200ms
桥接阈值: 300ms
============================================================
正在检测音频静音段: audio.wav
检测到 1250 个静音段
正在解析SRT文件: subtitles.srt
解析到 500 个字幕条目
正在优化字幕时间戳...
字幕 15: 修剪了 0.850s (10.000-13.500 → 10.200-13.000)
字幕 23: 修剪了 1.200s (25.000-28.000 → 25.400-27.600)
...
优化完成，总共节省了 45.230 秒

正在扩充字幕显示时间 (200ms)...
扩充统计:
  处理字幕: 500 个
  完全扩充: 456 个 (91.2%)
  部分扩充: 38 个 (7.6%)
  无法扩充: 6 个 (1.2%)
  解决冲突: 25 个
  总增加时间: 156.8 秒
扩充完成

正在桥接短间隔 (300ms)...
桥接统计:
  处理间隔: 956 个
  桥接阈值: 300ms
  消除间隔: 26.454 秒
桥接完成

正在保存优化后的SRT文件: optimized.srt
SRT文件保存完成
处理完成！
```

## 参数调优建议

### 静音阈值 (-n)

- **-25dB**: 严格模式，只检测明显的静音
- **-30dB**: 标准模式，适合大多数语音内容 (默认)
- **-35dB**: 敏感模式，检测更细微的停顿
- **-40dB**: 超敏感模式，可能误检轻微背景音

### 最小静音时长 (-d)

- **0.005s**: 检测极短停顿，适合快语速内容
- **0.01s**: 标准设置，平衡精度和稳定性 (默认)
- **0.05s**: 保守设置，只检测明显停顿
- **0.1s**: 非常保守，适合有背景音的内容

## 注意事项

1. **音频质量**: 低质量音频可能影响静音检测精度
2. **背景音乐**: 有持续背景音的内容可能需要调整参数
3. **文件编码**: SRT文件需要使用UTF-8编码
4. **处理时间**: 处理时间主要取决于音频文件大小

## 测试验证

运行测试脚本验证功能：

```bash
python test_optimizer.py
```

## 技术支持

如遇问题，请检查：
1. FFmpeg是否正确安装
2. 音频文件格式是否支持
3. SRT文件格式是否正确
4. 参数设置是否合理

---

**开发**: EvaTrans项目  
**版本**: v1.0  
**更新**: 2025-07-29
