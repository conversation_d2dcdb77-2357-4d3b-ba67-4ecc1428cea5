#!/usr/bin/env python3
"""
音频转WAV批量转换器
基于EvaTrans项目audio_processor.py设计的独立脚本

功能特性：
- 递归扫描所有音频/视频文件
- 转换为16bit WAV格式（高质量立体声）
- 多声道降混处理（复用原项目逻辑）
- 实时进度条显示（FFmpeg进度解析）
- 完整的日志记录和错误处理
- 输出到wav_output目录，保持原有结构

依赖：
- pip install tqdm ffmpeg-progress-yield
- 系统需要安装FFmpeg

作者：基于EvaTrans项目设计
"""

import os
import subprocess
import json
import time
import logging
import shutil
from pathlib import Path
from typing import List, Optional, Tuple, Dict
from dataclasses import dataclass
from datetime import datetime

from tqdm import tqdm
from ffmpeg_progress_yield import FfmpegProgress

# ============================================================================
# 配置常量
# ============================================================================

# FFmpeg路径和参数
FFMPEG_PATH = "ffmpeg"
FFPROBE_PATH = "ffprobe"
WAV_CODEC = "pcm_s16le"  # 16bit WAV编码器
THREADS = "4"
TIMEOUT = 3600  # FFmpeg超时（秒）
PROBE_TIMEOUT = 30  # FFprobe超时（秒）

# 输出目录名称
OUTPUT_DIR_NAME = "wav_output"

# 支持的音频/视频文件扩展名（复用原项目列表）
SUPPORTED_EXTENSIONS = {
    # 视频格式
    ".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm", ".m4v", ".3gp", ".3g2",
    ".mpg", ".mpeg", ".m2v", ".m4p", ".m4b", ".f4v", ".f4p", ".f4a", ".f4b",
    ".vob", ".ogv", ".ogg", ".drc", ".gif", ".gifv", ".mng", ".qt",
    ".yuv", ".rm", ".rmvb", ".asf", ".amv", ".mp2", ".mpe", ".mpv", ".svi", 
    ".mxf", ".roq", ".nsv", 
    # 音频格式
    ".wav", ".mp3", ".flac", ".aac", ".m4a", ".opus", ".aiff", ".au",
    ".ra", ".3ga", ".amr", ".awb", ".dct", ".dss", ".dvf", ".gsm", ".iklax", ".ivs",
    ".mmf", ".mpc", ".msv", ".nmf", ".oga", ".mogg", ".raw", ".sln", ".tta",
    ".voc", ".vox", ".wv", ".8svx", ".cda", ".wma"
}

# 复用原项目的专业声道降混滤镜映射表（基于音频工程学原理）
CHANNEL_FILTERS = {
    1: None,  # 单声道保持不变
    2: None,  # 立体声保持不变
    3: "pan=stereo|FL=FL+0.5*LFE|FR=FR+0.5*LFE",  # 2.1环绕
    4: "pan=stereo|FL=0.7*FL+0.3*BL|FR=0.7*FR+0.3*BR",  # 4.0环绕
    5: "pan=stereo|FL=0.6*FL+0.3*BL+0.4*LFE|FR=0.6*FR+0.3*BR+0.4*LFE",  # 4.1环绕
    6: "pan=stereo|FL=0.5*FL+0.707*FC+0.5*BL+0.3*LFE|FR=0.5*FR+0.707*FC+0.5*BR+0.3*LFE",  # 5.1环绕
    7: "pan=stereo|FL=0.4*FL+0.6*FC+0.4*BL+0.1*BC+0.3*LFE|FR=0.4*FR+0.6*FC+0.4*BR+0.1*BC+0.3*LFE",  # 6.1环绕
    8: "pan=stereo|FL=0.4*FL+0.6*FC+0.4*BL+0.2*SL+0.3*LFE|FR=0.4*FR+0.6*FC+0.4*BR+0.2*SR+0.3*LFE",  # 7.1环绕
}

# 超过8声道的降级处理滤镜（兜底策略）
FALLBACK_FILTER = "pan=stereo|FL=0.4*FL+0.6*FC+0.4*BL+0.2*SL+0.3*LFE|FR=0.4*FR+0.6*FC+0.4*BR+0.2*SR+0.3*LFE"

# ============================================================================
# 颜色方案配置
# ============================================================================

@dataclass
class ColorScheme:
    """进度条颜色方案配置"""
    name: str
    start_color: str      # 开始阶段颜色
    progress_color: str   # 进行阶段颜色
    fast_color: str       # 加速阶段颜色
    near_complete: str    # 接近完成颜色
    complete_color: str   # 完成阶段颜色
    success_text: str     # 成功文本颜色代码
    error_text: str       # 错误文本颜色代码

# 预定义颜色方案
COLOR_SCHEMES = {
    'professional': ColorScheme(
        name="专业蓝绿",
        start_color='#1f77b4',    # 专业蓝
        progress_color='#17becf',  # 青蓝色
        fast_color='#bcbd22',      # 黄绿色
        near_complete='#2ca02c',   # 成功绿
        complete_color='#00ff00',  # 亮绿色
        success_text='\033[92m',   # 绿色文本
        error_text='\033[91m'      # 红色文本
    ),
    'modern': ColorScheme(
        name="现代渐变",
        start_color='#667eea',     # 现代紫蓝
        progress_color='#764ba2',  # 紫色
        fast_color='#f093fb',      # 粉紫色
        near_complete='#00d4aa',   # 现代青绿
        complete_color='#4facfe',  # 亮蓝色
        success_text='\033[96m',   # 青色文本
        error_text='\033[95m'      # 紫色文本
    ),
    'ocean': ColorScheme(
        name="海洋蓝调",
        start_color='#2196F3',     # 蓝色
        progress_color='#03DAC6',  # 青绿色
        fast_color='#00BCD4',      # 青色
        near_complete='#4CAF50',   # 绿色
        complete_color='#8BC34A',  # 亮绿色
        success_text='\033[94m',   # 蓝色文本
        error_text='\033[91m'      # 红色文本
    ),
    'neon': ColorScheme(
        name="霓虹炫彩",
        start_color='#ff006e',     # 霓虹粉
        progress_color='#8338ec',  # 霓虹紫
        fast_color='#3a86ff',      # 霓虹蓝
        near_complete='#06ffa5',   # 霓虹绿
        complete_color='#ffbe0b',  # 霓虹黄
        success_text='\033[92m',   # 绿色文本
        error_text='\033[91m'      # 红色文本
    )
}

# ============================================================================
# 数据模型
# ============================================================================

@dataclass
class ConversionResult:
    """单个文件转换结果"""
    success: bool
    input_path: str
    output_path: str
    processing_time: float
    error_message: str = ""
    input_size: int = 0
    output_size: int = 0
    channels: int = 0
    duration: Optional[float] = None

@dataclass
class ConversionStats:
    """转换统计信息"""
    total_files: int = 0
    success_count: int = 0
    failed_count: int = 0
    total_time: float = 0.0
    total_input_size: int = 0
    total_output_size: int = 0
    failed_files: List[str] = None
    
    def __post_init__(self):
        if self.failed_files is None:
            self.failed_files = []

# ============================================================================
# 核心转换类
# ============================================================================

class WavConverter:
    """WAV转换器 - 基于原项目audio_processor.py设计"""

    def __init__(self, color_scheme: str = 'professional'):
        """初始化转换器

        Args:
            color_scheme: 颜色方案名称 ('professional', 'modern', 'ocean', 'neon')
        """
        self.setup_logging()
        self.color_scheme = COLOR_SCHEMES.get(color_scheme, COLOR_SCHEMES['professional'])
        print(f"🎨 使用颜色方案: {self.color_scheme.name}")
    
    def setup_logging(self):
        """设置日志记录"""
        log_filename = f"wav_conversion_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"开始WAV转换任务，日志文件：{log_filename}")
    
    def check_ffmpeg(self) -> bool:
        """检查FFmpeg是否可用"""
        try:
            subprocess.run([FFMPEG_PATH, "-version"], 
                         capture_output=True, check=True, timeout=10)
            subprocess.run([FFPROBE_PATH, "-version"], 
                         capture_output=True, check=True, timeout=10)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
            return False
    
    def scan_audio_files(self, root_dir: str) -> List[str]:
        """递归扫描音频/视频文件"""
        audio_files = []
        root_path = Path(root_dir)
        
        for file_path in root_path.rglob("*"):
            if file_path.is_file() and file_path.suffix.lower() in SUPPORTED_EXTENSIONS:
                # 跳过输出目录中的文件
                if OUTPUT_DIR_NAME not in file_path.parts:
                    audio_files.append(str(file_path))
        
        return sorted(audio_files)
    
    def probe_audio_info(self, file_path: str) -> Tuple[int, Optional[float]]:
        """使用FFprobe检测音频信息（复用原项目逻辑）"""
        try:
            cmd = [
                FFPROBE_PATH,
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_streams',
                '-select_streams', 'a:0',
                file_path
            ]

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=False,
                timeout=PROBE_TIMEOUT
            )

            stdout_text = result.stdout.decode('utf-8', errors='ignore') if result.stdout else ""
            stderr_text = result.stderr.decode('utf-8', errors='ignore') if result.stderr else ""

            if result.returncode != 0:
                raise Exception(f"FFprobe失败: {stderr_text}")

            data = json.loads(stdout_text)
            streams = data.get('streams', [])
            if not streams:
                raise Exception("未找到音频流")

            audio_stream = streams[0]
            channels = audio_stream.get('channels', 2)
            duration_str = audio_stream.get('duration')
            duration = float(duration_str) if duration_str else None

            return channels, duration

        except Exception as e:
            raise Exception(f"音频信息检测失败: {str(e)}")

    def detect_audio_codec(self, file_path: str) -> Optional[str]:
        """检测音频编码格式"""
        try:
            cmd = [
                FFPROBE_PATH,
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_streams',
                '-select_streams', 'a:0',
                file_path
            ]

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=False,
                timeout=PROBE_TIMEOUT
            )

            if result.returncode != 0:
                return None

            stdout_text = result.stdout.decode('utf-8', errors='ignore') if result.stdout else ""
            data = json.loads(stdout_text)
            streams = data.get('streams', [])

            if not streams:
                return None

            audio_stream = streams[0]
            codec_name = audio_stream.get('codec_name', '').lower()

            return codec_name

        except Exception:
            return None

    def is_16bit_wav_format(self, file_path: str) -> bool:
        """检测文件是否已经是16bit WAV格式"""
        try:
            # 检查文件扩展名
            if not file_path.lower().endswith('.wav'):
                return False

            codec = self.detect_audio_codec(file_path)
            if not codec:
                return False

            # 16bit WAV编码名称
            wav_16bit_codecs = ['pcm_s16le', 'pcm_s16be']
            return codec in wav_16bit_codecs

        except Exception:
            return False

    def copy_wav_stream(self, input_path: str, output_path: str, progress_callback=None) -> None:
        """复制16bit WAV音频流（不重新编码）"""
        try:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 构建FFmpeg命令：复制WAV流，不重新编码
            cmd = [
                FFMPEG_PATH,
                '-i', input_path,
                '-c:a', 'copy',        # 关键：复制音频流，不重新编码
                '-vn',                 # 不包含视频流
                '-threads', THREADS,   # 多线程处理
                '-y',                  # 覆盖输出文件
                output_path
            ]

            # 使用ffmpeg-progress-yield库执行复制
            with FfmpegProgress(cmd) as ff:
                for progress in ff.run_command_with_progress():
                    if progress_callback:
                        progress_callback(progress)

        except Exception as e:
            raise Exception(f"WAV流复制失败: {str(e)}")

    def get_file_duration(self, file_path: str) -> Optional[float]:
        """获取文件精确时长（用于进度计算）"""
        try:
            cmd = [
                FFPROBE_PATH,
                '-v', 'quiet',
                '-show_entries', 'format=duration',
                '-of', 'csv=p=0',
                file_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode != 0:
                return None

            duration_str = result.stdout.strip()
            if duration_str and duration_str != 'N/A':
                return float(duration_str)

            return None

        except (subprocess.TimeoutExpired, ValueError, Exception):
            return None



    def get_channel_filter(self, channels: int) -> Optional[str]:
        """获取声道处理滤镜（复用原项目逻辑）"""
        if channels <= 2:
            return None
        elif channels <= 8:
            return CHANNEL_FILTERS.get(channels)
        else:
            return FALLBACK_FILTER
    
    def convert_to_wav(self, input_path: str, output_path: str,
                       channel_filter: Optional[str],
                       progress_callback=None) -> None:
        """转换音频为16bit WAV（使用ffmpeg-progress-yield库）"""
        try:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 构建FFmpeg命令（不需要-progress参数，库会自动添加）
            cmd = [
                FFMPEG_PATH,
                '-i', input_path,
                '-c:a', WAV_CODEC,     # 使用16bit PCM编码器
                '-vn',                 # 不包含视频流
                '-ac', '2',            # 立体声（降混后）
                '-threads', THREADS,   # 4线程并行
                '-y',                  # 覆盖输出文件
                output_path
            ]

            # 添加声道处理滤镜（如果需要）
            if channel_filter:
                cmd.insert(-1, '-af')
                cmd.insert(-1, channel_filter)

            # 使用ffmpeg-progress-yield库执行转换
            with FfmpegProgress(cmd) as ff:
                for progress in ff.run_command_with_progress():
                    if progress_callback:
                        progress_callback(progress)

        except Exception as e:
            raise Exception(f"转换失败: {str(e)}")



    def convert_file(self, input_path: str, output_dir: str) -> ConversionResult:
        """转换单个文件"""
        start_time = time.time()

        # 生成输出路径，保持目录结构
        rel_path = os.path.relpath(input_path, ".")
        output_path = os.path.join(output_dir, rel_path)
        output_path = os.path.splitext(output_path)[0] + ".m4a"

        try:
            # 获取输入文件大小
            input_size = os.path.getsize(input_path)

            # 检测音频信息
            channels, duration = self.probe_audio_info(input_path)

            # 生成声道处理滤镜
            channel_filter = self.get_channel_filter(channels)

            # 执行转换
            self.convert_to_aac(input_path, output_path, channel_filter)

            # 获取输出文件大小
            output_size = os.path.getsize(output_path)
            processing_time = time.time() - start_time

            # 记录成功日志
            self.logger.info(f"转换成功: {os.path.basename(input_path)} -> {os.path.basename(output_path)} "
                           f"({processing_time:.1f}s, {channels}ch, 256k)")

            return ConversionResult(
                success=True,
                input_path=input_path,
                output_path=output_path,
                processing_time=processing_time,
                input_size=input_size,
                output_size=output_size,
                channels=channels,
                duration=duration
            )

        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = str(e)

            # 记录失败日志
            self.logger.error(f"转换失败: {os.path.basename(input_path)} - {error_msg}")

            return ConversionResult(
                success=False,
                input_path=input_path,
                output_path=output_path,
                processing_time=processing_time,
                error_message=error_msg
            )

    def convert_file_with_progress(self, input_path: str, output_dir: str,
                                 progress_callback=None) -> ConversionResult:
        """转换单个文件（支持进度回调，16bit WAV文件直接复制）"""
        start_time = time.time()

        # 生成输出路径，保持目录结构
        rel_path = os.path.relpath(input_path, ".")
        output_path = os.path.join(output_dir, rel_path)
        output_path = os.path.splitext(output_path)[0] + ".wav"

        try:
            # 获取输入文件大小
            input_size = os.path.getsize(input_path)

            # 检测音频信息
            channels, duration = self.probe_audio_info(input_path)

            # 检测是否已经是16bit WAV格式
            if self.is_16bit_wav_format(input_path):
                # 已经是16bit WAV格式，复制音频流（不重新编码）
                self.copy_wav_stream(input_path, output_path, progress_callback)

                # 获取输出文件大小
                output_size = os.path.getsize(output_path)
                processing_time = time.time() - start_time

                # 记录复制日志
                self.logger.info(f"WAV复制: {os.path.basename(input_path)} -> {os.path.basename(output_path)} "
                               f"({processing_time:.1f}s, {channels}ch, 流复制)")
            else:
                # 需要转换为16bit WAV
                # 生成声道处理滤镜
                channel_filter = self.get_channel_filter(channels)

                # 执行转换（带进度回调）
                self.convert_to_wav(input_path, output_path, channel_filter, progress_callback)

                # 获取输出文件大小
                output_size = os.path.getsize(output_path)
                processing_time = time.time() - start_time

                # 记录转换日志
                self.logger.info(f"转换成功: {os.path.basename(input_path)} -> {os.path.basename(output_path)} "
                               f"({processing_time:.1f}s, {channels}ch, 16bit WAV)")

            return ConversionResult(
                success=True,
                input_path=input_path,
                output_path=output_path,
                processing_time=processing_time,
                input_size=input_size,
                output_size=output_size,
                channels=channels,
                duration=duration
            )

        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = str(e)

            # 记录失败日志
            self.logger.error(f"转换失败: {os.path.basename(input_path)} - {error_msg}")

            return ConversionResult(
                success=False,
                input_path=input_path,
                output_path=output_path,
                processing_time=processing_time,
                input_size=input_size,
                output_size=0,
                channels=0,
                duration=0,
                error_message=error_msg
            )

    def get_progress_color(self, progress: float) -> str:
        """根据进度返回动态颜色"""
        if progress < 25:
            return self.color_scheme.start_color      # 开始阶段
        elif progress < 50:
            return self.color_scheme.progress_color   # 进行阶段
        elif progress < 75:
            return self.color_scheme.fast_color       # 加速阶段
        elif progress < 95:
            return self.color_scheme.near_complete    # 接近完成
        else:
            return self.color_scheme.complete_color   # 完成阶段

    def get_progress_ansi_color(self, progress: float) -> str:
        """根据进度返回ANSI颜色代码（用于终端显示）"""
        if progress < 25:
            return '\033[94m'  # 蓝色
        elif progress < 50:
            return '\033[96m'  # 青色
        elif progress < 75:
            return '\033[93m'  # 黄色
        elif progress < 95:
            return '\033[92m'  # 绿色
        else:
            return '\033[91m'  # 亮绿色（用红色代替，因为终端限制）

    def create_colored_bar(self, progress: float, width: int = 20) -> str:
        """创建彩色进度条字符串"""
        filled_width = int(progress / 100 * width)

        # 根据进度选择颜色
        color = self.get_progress_ansi_color(progress)

        # 创建彩色进度条
        filled_part = '█' * filled_width
        empty_part = '░' * (width - filled_width)

        return f"{color}{filled_part}\033[0m{empty_part}"

    def truncate_filename(self, filename: str, max_length: int = 40) -> str:
        """截断过长的文件名"""
        if len(filename) <= max_length:
            return filename

        # 保留文件扩展名
        name, ext = os.path.splitext(filename)
        if len(ext) > 10:  # 扩展名太长的情况
            ext = ext[:10]

        # 计算可用长度
        available_length = max_length - len(ext) - 3  # 3个字符用于"..."

        if available_length > 0:
            return name[:available_length] + "..." + ext
        else:
            return filename[:max_length]

    def batch_convert(self, files: List[str], output_dir: str) -> ConversionStats:
        """批量转换文件，使用彩色单层进度条显示"""
        stats = ConversionStats(total_files=len(files))

        for i, file_path in enumerate(files):
            filename = os.path.basename(file_path)
            # 截断过长的文件名以确保单行显示
            display_name = self.truncate_filename(filename, 35)
            current_color = self.color_scheme.start_color  # 初始颜色

            # 检测是否为16bit WAV格式
            is_wav = self.is_16bit_wav_format(file_path)
            action_emoji = "📋" if is_wav else "🎵"  # 流复制用📋，转换用🎵

            # 简化的单层进度条：确保单行显示
            with tqdm(
                total=100,
                desc=f"{action_emoji} [{i+1}/{len(files)}] {display_name}",
                unit="%",
                ncols=90,   # 进一步减少宽度
                leave=False,  # 完成后清除进度条
                dynamic_ncols=False,  # 禁用动态宽度，避免布局变化
                miniters=1,  # 减少更新频率
                mininterval=0.1  # 最小更新间隔0.1秒
            ) as progress_pbar:

                def progress_callback(progress):
                    """进度回调函数 - 简化版本避免多行输出"""
                    # 更新进度
                    progress_pbar.update(progress - progress_pbar.n)

                    # 只在关键阶段更新emoji，减少频繁更新
                    current_emoji = None
                    if is_wav:
                        # WAV流复制
                        if progress >= 95:
                            current_emoji = "✅"  # 复制完成
                        elif progress < 5:
                            current_emoji = "📋"  # 复制开始
                    else:
                        # 其他格式转换
                        if progress >= 95:
                            current_emoji = "🎉"  # 转换完成
                        elif progress >= 75:
                            current_emoji = "⚡"  # 转换加速
                        elif progress >= 50:
                            current_emoji = "🚀"  # 转换进行
                        elif progress < 5:
                            current_emoji = "🎵"  # 转换开始

                    # 只在emoji变化时更新描述
                    if current_emoji and not progress_pbar.desc.startswith(current_emoji):
                        new_desc = f"{current_emoji} [{i+1}/{len(files)}] {display_name}"
                        progress_pbar.set_description(new_desc)

                    # 简化后缀，只显示基本信息
                    if progress % 10 == 0 or progress >= 95:  # 每10%或接近完成时更新
                        action = "复制" if is_wav else "转换"
                        progress_pbar.set_postfix_str(f"{action} 成功:{stats.success_count}")

                # 执行转换（带实时进度）
                result = self.convert_file_with_progress(file_path, output_dir, progress_callback)

            # 更新统计信息并输出彩色结果
            if result.success:
                stats.success_count += 1
                stats.total_input_size += result.input_size
                stats.total_output_size += result.output_size
                # 根据是否为WAV文件显示不同的完成信息
                if is_wav:
                    print(f"✅ {self.color_scheme.success_text}{filename}\033[0m - {self.color_scheme.success_text}WAV流复制完成\033[0m")
                else:
                    print(f"🎉 {self.color_scheme.success_text}{filename}\033[0m - {self.color_scheme.success_text}转换完成\033[0m")
            else:
                stats.failed_count += 1
                stats.failed_files.append(file_path)
                # 使用颜色方案的彩色emoji输出失败信息
                action = "流复制" if is_wav else "转换"
                print(f"❌ {self.color_scheme.error_text}{filename}\033[0m - {self.color_scheme.error_text}{action}失败: {result.error_message}\033[0m")

        return stats



# ============================================================================
# 工具函数
# ============================================================================

def format_file_size(size_bytes: int) -> str:
    """格式化文件大小显示"""
    if size_bytes == 0:
        return "0 B"

    units = ['B', 'KB', 'MB', 'GB', 'TB']
    unit_index = 0
    size = float(size_bytes)

    while size >= 1024 and unit_index < len(units) - 1:
        size /= 1024
        unit_index += 1

    return f"{size:.1f} {units[unit_index]}"

def format_duration(seconds: float) -> str:
    """格式化时间显示"""
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = int(seconds % 60)
        return f"{minutes}分{secs}秒"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        return f"{hours}小时{minutes}分钟"

def display_summary_report(stats: ConversionStats, total_time: float):
    """显示总结报告"""
    print("\n" + "="*50)
    print("转换完成报告")
    print("="*50)

    print(f"总文件数: {stats.total_files}")
    print(f"成功转换: {stats.success_count}")
    print(f"转换失败: {stats.failed_count}")
    print(f"总耗时: {format_duration(total_time)}")

    if stats.total_input_size > 0:
        print(f"输入总大小: {format_file_size(stats.total_input_size)}")
        print(f"输出总大小: {format_file_size(stats.total_output_size)}")

        if stats.total_input_size > 0:
            size_ratio = (stats.total_output_size / stats.total_input_size) * 100
            print(f"大小比率: {size_ratio:.1f}%")

    if stats.success_count > 0:
        avg_time = total_time / stats.success_count
        print(f"平均处理时间: {avg_time:.1f}秒/文件")

    # 如果有失败的文件，显示失败列表
    if stats.failed_files:
        print("\n转换失败的文件：")
        for failed_file in stats.failed_files:
            print(f"• {os.path.basename(failed_file)}")

    print("="*50)

# ============================================================================
# 主函数
# ============================================================================

def main():
    """主函数"""
    # 显示欢迎信息
    print("="*60)
    print("音频转WAV批量转换器")
    print("基于EvaTrans项目设计")
    print("支持多种音频/视频格式，智能声道处理")
    print("输出：16bit高质量WAV")
    print("="*60)

    # 创建转换器实例
    converter = WavConverter()

    # 检查FFmpeg
    print("检查FFmpeg环境...")
    if not converter.check_ffmpeg():
        print("❌ 错误：未找到FFmpeg或FFprobe，请先安装FFmpeg")
        print("安装指南：https://ffmpeg.org/download.html")
        return
    print("✓ FFmpeg环境检查通过")

    # 扫描文件
    print("扫描音频/视频文件...")
    current_dir = os.getcwd()
    files = converter.scan_audio_files(current_dir)

    if not files:
        print("未找到支持的音频/视频文件")
        return

    print(f"✓ 找到 {len(files)} 个文件待转换")

    # 显示文件类型统计
    extensions = {}
    for file_path in files:
        ext = os.path.splitext(file_path)[1].lower()
        extensions[ext] = extensions.get(ext, 0) + 1

    print("文件类型分布：")
    for ext, count in sorted(extensions.items()):
        print(f"  {ext}: {count} 个文件")

    # 设置输出目录
    output_dir = os.path.join(current_dir, OUTPUT_DIR_NAME)
    print(f"输出目录：{output_dir}")

    # 开始转换
    print("\n开始批量转换...")

    start_time = time.time()
    stats = converter.batch_convert(files, output_dir)
    total_time = time.time() - start_time

    # 显示总结报告
    display_summary_report(stats, total_time)

    # 最终状态
    if stats.failed_count == 0:
        print("\n🎉 所有文件转换完成！")
    else:
        print(f"\n⚠️  转换完成，但有 {stats.failed_count} 个文件失败")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n用户中断转换")
    except Exception as e:
        print(f"\n程序异常：{str(e)}")
