#!/usr/bin/env python3
"""
EvaTrans主应用模块 - 音视频处理和字幕生成的核心应用
"""

import os
import sys
import json
import flet as ft
from typing import Dict, Any, Optional, List
from enum import Enum

import threading
import time

# 全局编码环境配置
os.environ['PYTHONIOENCODING'] = 'utf-8'
if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8')
if hasattr(sys.stderr, 'reconfigure'):
    sys.stderr.reconfigure(encoding='utf-8')
# 项目根目录路径配置
src_dir = os.path.dirname(os.path.dirname(__file__))
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

# 核心模块导入
from dataclasses import dataclass

from .config_manager import ConfigManager
from .log_system import LogSystem
from .theme_manager import ThemeManager
from .settings_manager import SettingsManager
from services.llm import get_multi_llm_service, LLMConfig, APIFormat


# EvaTrans智能状态架构 (EISA) - 基于ASR-LLM组合的统一状态管理系统

class CombinationStage(Enum):
    """ASR-LLM组合处理阶段枚举

    每个枚举值包含6个属性：
    (display_text, icon, base_progress, cancellable, stage_id, is_active)

    - display_text: 显示文本
    - icon: 状态图标
    - base_progress: 基础进度百分比
    - cancellable: 是否可取消
    - stage_id: 阶段标识符
    - is_active: 是否为活跃处理状态
    """
    # 静态处理阶段（基于文件状态检测）
    AUDIO_PROCESSING = ("音频处理进行中", "🔄", 5, True, "audio", False)
    TRANSCRIPTION_PROCESSING = ("音频转录进行中", "🔄", 20, False, "transcription", False)
    SUBTITLE_PROCESSING = ("字幕生成进行中", "🔄", 40, False, "subtitle", False)
    COMPLETED = ("已完成", "✅", 100, False, "completed", False)
    FAILED = ("处理失败", "❌", 0, False, "failed", False)

    # 动态处理阶段（基于实际处理状态）
    AUDIO_PROCESSING_ACTIVE = ("音频处理进行中", "🔄", 8, True, "audio", True)
    TRANSCRIPTION_PROCESSING_ACTIVE = ("音频转录进行中", "🔄", 35, False, "transcription", True)  # 活跃转录处理
    SUBTITLE_PROCESSING_ACTIVE = ("字幕生成进行中", "🔄", 75, False, "subtitle", True)    # 活跃字幕生成

    def __init__(self, display_text, icon, base_progress, cancellable, stage_id, is_active):
        self.display_text = display_text
        self.icon = icon
        self.base_progress = base_progress
        self.cancellable = cancellable  # 是否可取消
        self.stage_id = stage_id  # 阶段标识符
        self.is_active = is_active  # 是否为活跃处理状态



@dataclass
class CombinationProgress:
    """ASR-LLM组合进度数据类 - 存储实时进度信息"""
    stage: CombinationStage
    progress_percent: int
    detail_text: str = ""


class EISAStateFusion:
    """状态融合器 - 整合多个状态信息源进行综合分析

    融合的状态信息包括：
    - 线程状态：转换线程活跃状态和线程计数
    - 文件状态：音频、转录、字幕文件的存在性检查
    - 处理指示器：状态文本中的关键词检测和分类
    """

    def __init__(self, main_app):
        self.main_app = main_app

    def fuse_task_state(self, task) -> dict:
        """融合任务的所有状态信息源

        Returns:
            dict: 包含thread_state、task_status、current_stage、
                  file_state、processing_indicators的融合状态字典
        """
        try:
            return {
                'thread_state': self._get_thread_state(),
                'task_status': task.get('status', ''),
                'current_stage': task.get('current_stage', ''),
                'file_state': self._get_file_state(task),
                'processing_indicators': self._get_processing_indicators(task),
                'task': task  # 任务信息用于详细文本生成
            }
        except Exception as e:
            self.main_app.log_message(f"状态融合异常: {str(e)}")
            return {
                'thread_state': {'conversion_active': False, 'thread_count': 0},
                'task_status': '',
                'current_stage': '',
                'file_state': {'has_audio': False, 'has_transcription': False, 'has_subtitle': False},
                'processing_indicators': {'has_processing_keywords': False, 'status_category': 'unknown'}
            }

    def _get_thread_state(self) -> dict:
        """获取线程状态信息"""
        import threading
        return {
            'conversion_active': (hasattr(self.main_app, 'conversion_thread') and
                                self.main_app.conversion_thread is not None and
                                self.main_app.conversion_thread.is_alive()),
            'thread_count': threading.active_count()
        }

    def _get_file_state(self, task) -> dict:
        """获取文件状态信息"""
        try:
            input_file = task.get('path', '')
            if not input_file:
                return {'has_audio': False, 'has_transcription': False, 'has_subtitle': False}

            task_folder = self.main_app._get_task_project_folder(input_file)
            if not task_folder:
                return {'has_audio': False, 'has_transcription': False, 'has_subtitle': False}

            file_name = os.path.splitext(os.path.basename(input_file))[0]

            # 检查字幕文件
            srt_file = os.path.join(task_folder, f"{file_name}.srt")
            has_subtitle = os.path.exists(srt_file) and os.path.getsize(srt_file) > 0

            # 检查转录文件
            has_transcription = self.main_app._has_valid_transcriptions(task_folder, file_name)

            # 检查音频文件
            audio_file_path = self.main_app._find_any_audio_file(task_folder, file_name)
            has_audio = audio_file_path is not None

            return {
                'has_audio': has_audio,
                'has_transcription': has_transcription,
                'has_subtitle': has_subtitle
            }
        except Exception as e:
            self.main_app.log_message(f"获取文件状态异常: {str(e)}")
            return {'has_audio': False, 'has_transcription': False, 'has_subtitle': False}

    def _get_processing_indicators(self, task) -> dict:
        """获取处理指示器"""
        status = task.get('status', '')

        # 处理关键词检测
        processing_keywords = [
            '进行中', '处理中', '准备中', '开始处理', '转换中',
            '转录中', '生成中', '字幕生成:'
        ]

        has_processing_keywords = any(keyword in status for keyword in processing_keywords)

        # 状态分类
        status_category = self._categorize_status(status)

        return {
            'has_processing_keywords': has_processing_keywords,
            'status_category': status_category
        }

    def _categorize_status(self, status: str) -> str:
        """对状态进行分类"""
        if '已完成' in status or '完成' in status:
            return 'completed'
        elif '失败' in status or '错误' in status:
            return 'failed'
        elif '音频' in status and ('处理' in status or '转换' in status):
            return 'audio_processing'
        elif '转录' in status:
            return 'transcription'
        elif '字幕' in status or '生成' in status:
            return 'subtitle_generation'
        elif any(keyword in status for keyword in ['进行中', '处理中']):
            return 'processing'
        elif '等待' in status:
            return 'waiting'
        else:
            return 'unknown'


class CombinationProgressCalculator:
    """进度计算器 - 基于状态融合的智能进度计算

    使用三级优先级推断算法：
    1. 明确设置的阶段（TaskFlowController设置）
    2. 动态推断（基于线程状态和状态关键词）
    3. 静态推断（基于文件存在性检查）
    """

    def __init__(self, main_app):
        self.main_app = main_app
        self.state_fusion = EISAStateFusion(main_app)

    def calculate_progress(self, task) -> CombinationProgress:
        """基于状态融合的智能进度计算

        处理流程：
        1. 融合多源状态信息
        2. 三级优先级阶段推断
        3. 精确进度百分比计算
        4. 详细描述文本生成

        Returns:
            CombinationProgress: 包含阶段、进度和描述的进度对象
        """
        try:
            input_file = task.get('path', '')
            if not input_file:
                return CombinationProgress(CombinationStage.AUDIO_PROCESSING, 0)

            # 状态融合：整合所有状态信息源
            fused_state = self.state_fusion.fuse_task_state(task)

            # 智能状态推断
            inferred_stage = self._infer_stage_from_fused_state(fused_state, task)

            # 计算精确进度
            progress_percent = self._calculate_precise_progress(inferred_stage, fused_state, task)

            # 生成详细描述
            detail_text = self._generate_detail_text(inferred_stage, fused_state)

            return CombinationProgress(inferred_stage, progress_percent, detail_text)

        except Exception as e:
            self.main_app.log_message(f"融合计算异常: {str(e)}")
            return CombinationProgress(CombinationStage.FAILED, 0)

    def _infer_stage_from_fused_state(self, fused_state: dict, task) -> CombinationStage:
        """从融合状态推断当前处理阶段

        三级优先级推断逻辑：
        1. 优先级1：TaskFlowController明确设置的current_stage
        2. 优先级2：基于线程活跃状态和状态关键词的动态推断
        3. 优先级3：基于文件存在性的静态推断

        Returns:
            CombinationStage: 推断出的当前处理阶段
        """

        # 优先级1：明确设置的阶段
        current_stage = fused_state['current_stage']
        if current_stage:
            stage_mapping = {
                'audio_processing': CombinationStage.AUDIO_PROCESSING,
                'transcription': CombinationStage.TRANSCRIPTION_PROCESSING,
                'subtitle_generation': CombinationStage.SUBTITLE_PROCESSING,
                'completed': CombinationStage.COMPLETED,
                'failed': CombinationStage.FAILED
            }
            mapped_stage = stage_mapping.get(current_stage)
            if mapped_stage:
                # 检查是否为活跃状态
                if self._is_active_processing(fused_state):
                    return self._get_active_variant(mapped_stage)
                return mapped_stage

        # 优先级2：基于线程状态和处理指示器的动态推断
        if (fused_state['thread_state']['conversion_active'] or
            fused_state['processing_indicators']['has_processing_keywords']):
            # 根据状态关键词推断具体阶段
            return self._infer_from_status_keywords(fused_state['task_status'])

        # 优先级3：基于文件状态的静态推断
        return self._infer_from_file_state(fused_state['file_state'])

    def _is_active_processing(self, fused_state: dict) -> bool:
        """判断是否为活跃处理状态"""
        return (fused_state['thread_state']['conversion_active'] or
                fused_state['processing_indicators']['has_processing_keywords'])

    def _get_active_variant(self, static_stage: CombinationStage) -> CombinationStage:
        """获取静态阶段对应的活跃变体"""
        active_mapping = {
            CombinationStage.AUDIO_PROCESSING: CombinationStage.AUDIO_PROCESSING_ACTIVE,
            CombinationStage.TRANSCRIPTION_PROCESSING: CombinationStage.TRANSCRIPTION_PROCESSING_ACTIVE,
            CombinationStage.SUBTITLE_PROCESSING: CombinationStage.SUBTITLE_PROCESSING_ACTIVE
        }
        return active_mapping.get(static_stage, static_stage)

    def _infer_from_status_keywords(self, status: str) -> CombinationStage:
        """根据状态关键词推断活跃阶段"""
        if '音频' in status and ('处理' in status or '转换' in status):
            return CombinationStage.AUDIO_PROCESSING_ACTIVE
        elif '转录' in status:
            return CombinationStage.TRANSCRIPTION_PROCESSING_ACTIVE
        elif '字幕' in status or '生成' in status:
            return CombinationStage.SUBTITLE_PROCESSING_ACTIVE
        else:
            # 通用的进行中状态，默认为音频处理
            return CombinationStage.AUDIO_PROCESSING_ACTIVE

    def _infer_from_file_state(self, file_state: dict) -> CombinationStage:
        """基于文件状态的静态推断"""
        if file_state['has_subtitle']:
            return CombinationStage.COMPLETED
        elif file_state['has_transcription']:
            return CombinationStage.SUBTITLE_PROCESSING
        elif file_state['has_audio']:
            return CombinationStage.TRANSCRIPTION_PROCESSING
        else:
            return CombinationStage.AUDIO_PROCESSING

    def _calculate_precise_progress(self, stage: CombinationStage, fused_state: dict, task) -> int:
        """计算精确进度百分比

        进度计算优先级：
        1. 分段处理进度（音频处理阶段）：基于已完成分段数量
        2. 分段阶段进度（转录/字幕阶段）：基于ASR-LLM组合完成度
        3. 基础进度：使用阶段的base_progress属性
        4. 活跃状态微调：活跃状态进度+3%

        Returns:
            int: 0-100的进度百分比
        """

        # 优先检查分段处理进度（音频处理阶段）
        if stage in [CombinationStage.AUDIO_PROCESSING, CombinationStage.AUDIO_PROCESSING_ACTIVE]:
            segmentation_progress = self._calculate_segmentation_progress(task)
            if segmentation_progress is not None:
                return segmentation_progress

        # 检查分段处理的其他阶段（转录/字幕阶段）
        segmented_progress = self._calculate_segmented_stage_progress(stage, task)
        if segmented_progress is not None:
            return segmented_progress

        # 使用阶段的基础进度
        base_progress = stage.base_progress

        # 活跃状态微调
        if stage.is_active:
            return min(base_progress + 3, 95)  # 活跃状态+3%，最高95%

        return base_progress

    def _calculate_segmentation_progress(self, task):
        """计算分段处理进度（音频处理阶段）

        使用SegmentationUtils检测分段文件并计算完成度：
        - 检测分段文件存在性
        - 统计已完成的分段数量
        - 计算进度：5% + (5% * 完成分段数 / 总分段数)
        - 全部完成时返回10%（音频处理阶段完成）

        Returns:
            int: 分段处理进度百分比，非分段处理返回None
        """
        try:
            from .segmentation_utils import SegmentationUtils

            input_file = task.get('path', '')
            task_folder = self.main_app._get_task_project_folder(input_file)
            file_name = os.path.splitext(os.path.basename(input_file))[0]

            # 检查是否存在分段文件
            segment_files = SegmentationUtils.find_segment_files(task_folder, file_name)

            if not segment_files:
                return None  # 不是分段处理

            # 计算分段进度
            total_segments = len(segment_files)
            enabled_asr_services = self.main_app._get_enabled_asr_services()
            enabled_llm_apis = self.main_app._get_enabled_llm_apis()

            completed_segments = SegmentationUtils.count_completed_segments(
                task_folder, segment_files, enabled_asr_services, enabled_llm_apis)

            if completed_segments == total_segments:
                # 所有分段完成
                return 10  # 音频处理阶段完成
            else:
                # 分段处理中
                progress = 5 + int(5 * completed_segments / total_segments)
                return progress

        except Exception as e:
            self.main_app.log_message(f"分段进度计算异常: {str(e)}")
            return None

    def _calculate_segmented_stage_progress(self, stage: CombinationStage, task):
        """计算分段处理的转录和字幕阶段进度

        基于ASR-LLM组合完成度计算进度：
        - 转录阶段：20% + (20% * 转录完成组合数 / 总组合数)
        - 字幕阶段：40% + (55% * 字幕完成组合数 / 总组合数)

        使用_analyze_asr_llm_combinations_status分析组合状态

        Returns:
            int: 阶段进度百分比，非分段处理返回None
        """
        try:
            from .segmentation_utils import SegmentationUtils

            input_file = task.get('path', '')
            task_folder = self.main_app._get_task_project_folder(input_file)
            file_name = os.path.splitext(os.path.basename(input_file))[0]

            # 检查是否存在分段文件
            segment_files = SegmentationUtils.find_segment_files(task_folder, file_name)

            if not segment_files:
                return None  # 不是分段处理

            # 分析分段状态
            status = self.main_app._analyze_asr_llm_combinations_status(task_folder, file_name)

            if not status or not status.get('is_segmented', False):
                return None

            total_combinations = status['total_combinations']
            transcription_ready = status['transcription_ready']
            subtitle_ready = status['subtitle_ready']

            if stage == CombinationStage.TRANSCRIPTION_PROCESSING:
                # 转录阶段进度计算
                if total_combinations > 0:
                    transcription_progress = transcription_ready / total_combinations
                    return int(20 + 20 * transcription_progress)
                return 20

            elif stage == CombinationStage.SUBTITLE_PROCESSING:
                # 字幕阶段进度计算
                if total_combinations > 0:
                    subtitle_progress = subtitle_ready / total_combinations
                    return int(40 + 55 * subtitle_progress)
                return 40

            return None

        except Exception as e:
            self.main_app.log_message(f"分段阶段进度计算异常: {str(e)}")
            return None

    def _generate_detail_text(self, stage: CombinationStage, fused_state: dict) -> str:
        """生成详细描述文本 - 集成分段信息显示"""

        # 优先检查分段信息
        segmented_detail = self._generate_segmented_detail_text(stage, fused_state)
        if segmented_detail:
            return segmented_detail

        if stage.is_active:
            # 活跃状态显示更具体的信息
            if stage == CombinationStage.AUDIO_PROCESSING_ACTIVE:
                return "正在处理音频文件"
            elif stage == CombinationStage.TRANSCRIPTION_PROCESSING_ACTIVE:
                return "正在进行语音转录"
            elif stage == CombinationStage.SUBTITLE_PROCESSING_ACTIVE:
                return "正在生成字幕文件"

        # 静态状态使用默认描述
        return ""

    def _generate_segmented_detail_text(self, stage: CombinationStage, fused_state: dict) -> str:
        """生成分段处理的详细描述 - 使用SegmentationUtils工具类"""
        try:
            from .segmentation_utils import SegmentationUtils

            # 从fused_state中获取任务信息
            task = fused_state.get('task')
            if not task:
                return ""

            input_file = task.get('path', '')
            task_folder = self.main_app._get_task_project_folder(input_file)
            file_name = os.path.splitext(os.path.basename(input_file))[0]

            # 检查是否存在分段文件
            segment_files = SegmentationUtils.find_segment_files(task_folder, file_name)

            if not segment_files:
                return ""  # 不是分段处理

            total_segments = len(segment_files)

            # 分析分段状态
            status = self.main_app._analyze_asr_llm_combinations_status(task_folder, file_name)

            if not status or not status.get('is_segmented', False):
                return ""

            completed_segments = status.get('completed_segments', 0)

            if stage == CombinationStage.AUDIO_PROCESSING_ACTIVE:
                return f"正在处理分段 {completed_segments + 1}/{total_segments}"
            elif stage == CombinationStage.TRANSCRIPTION_PROCESSING:
                return f"分段转录进行中 ({completed_segments}/{total_segments} 完成)"
            elif stage == CombinationStage.SUBTITLE_PROCESSING:
                return f"分段字幕生成中 ({completed_segments}/{total_segments} 完成)"
            elif stage == CombinationStage.COMPLETED:
                return f"所有分段处理完成 ({total_segments}/{total_segments})"

            return ""

        except Exception as e:
            self.main_app.log_message(f"生成分段详细信息异常: {str(e)}")
            return ""




class TaskFlowController:
    """任务流转控制器 - 管理任务在不同处理阶段间的智能流转

    核心功能：
    - 当前处理阶段检测
    - 增量处理决策逻辑
    - 音频处理阶段控制
    - 转录服务调度
    - 字幕生成管理
    - 分段处理支持

    特性：
    - 可配置的流转逻辑开关
    - 调试模式支持
    - 处理统计信息收集
    """

    def __init__(self, main_app):
        """初始化任务流转控制器

        设置配置开关、调试模式和统计信息收集器

        Args:
            main_app: 主应用实例，提供配置和日志接口
        """
        self.main_app = main_app
        self.progress_calculator = main_app.progress_calculator

        # 配置开关：是否启用新的流转逻辑（从配置读取，默认启用）
        self.enable_new_flow = getattr(main_app.config_manager, 'config', {}).get('enable_task_flow_controller', True)

        # 调试模式：记录详细的流转日志（从配置读取，默认关闭）
        self.debug_mode = getattr(main_app.config_manager, 'config', {}).get('task_flow_debug_mode', False)

        if self.debug_mode:
            self.main_app.log_message(f"[TaskFlow] 初始化完成 - 新流转逻辑: {'启用' if self.enable_new_flow else '禁用'}")

        # 统计信息（用于监控和调试）
        self.stats = {
            'total_tasks_processed': 0,
            'success_count': 0,
            'error_count': 0
        }

    def execute_task_flow(self, task: Dict, task_index: int) -> bool:
        """执行任务流转逻辑

        根据当前任务状态智能选择下一步处理动作：
        - 检测当前处理阶段
        - 执行增量处理决策
        - 触发相应的处理流程

        Args:
            task: 任务对象，包含文件路径和状态信息
            task_index: 任务在列表中的索引位置

        Returns:
            bool: 处理成功返回True，失败返回False
        """
        try:
            if self.debug_mode:
                self.main_app.log_message(f"[TaskFlow] 开始处理任务: {task.get('name', 'Unknown')}")

            # 获取任务基本信息
            input_file = task.get('path', '')
            if not input_file:
                self.main_app.log_message("[TaskFlow] 错误：任务路径为空")
                return False

            task_folder = self.main_app._get_task_project_folder(input_file)
            if not task_folder:
                self.main_app.log_message("[TaskFlow] 错误：无法获取任务文件夹")
                return False

            file_name = os.path.splitext(os.path.basename(input_file))[0]

            # 1. 检测当前阶段
            current_stage = self.detect_current_stage(task_folder, file_name)

            if self.debug_mode:
                self.main_app.log_message(f"[TaskFlow] 检测到当前阶段: {current_stage.display_text}")

            # 2. 增量处理决策
            flow_action = self.handle_incremental_processing(task_folder, file_name, current_stage)

            if self.debug_mode:
                self.main_app.log_message(f"[TaskFlow] 流转决策: {flow_action.get('action', 'unknown')}")

            # 3. 执行相应的处理逻辑
            return self._execute_flow_action(task, task_index, flow_action)

        except Exception as e:
            self.main_app.log_message(f"[TaskFlow] 任务流转异常: {str(e)}")
            self.stats['error_count'] += 1
            return False

    def detect_current_stage(self, task_folder: str, file_name: str) -> CombinationStage:
        """检测任务当前处理阶段

        检测逻辑：
        1. 检查合并字幕文件完整性
        2. 检查分段字幕状态
        3. 检查转录文件状态
        4. 检查音频文件状态

        Args:
            task_folder: 任务文件夹路径
            file_name: 原始文件名（不含扩展名）

        Returns:
            CombinationStage: 当前处理阶段枚举值
        """
        try:
            # === 第一层：检查最终产品（合并字幕） ===
            merged_status = self._check_merged_subtitles_status(task_folder, file_name)

            if merged_status['complete']:
                return CombinationStage.COMPLETED
            elif merged_status['partial']:
                # 有部分合并文件，但不完整 - 需要继续字幕生成
                return CombinationStage.SUBTITLE_PROCESSING

            # === 第二层：检查中间产物（分段字幕） ===
            segment_status = self._check_segment_subtitles_status(task_folder, file_name)

            if segment_status['ready_for_merge']:
                # 分段字幕完整，需要合并
                return CombinationStage.SUBTITLE_PROCESSING
            elif segment_status['partial']:
                # 分段字幕部分完成，需要继续生成
                return CombinationStage.SUBTITLE_PROCESSING

            # === 第三层：检查转录文件 ===
            if self.main_app._find_existing_transcriptions(task_folder, file_name):
                return CombinationStage.SUBTITLE_PROCESSING

            # === 第四层：检查音频文件 ===
            if self.main_app._find_any_audio_file(task_folder, file_name):
                return CombinationStage.TRANSCRIPTION_PROCESSING

            # === 默认：需要音频处理 ===
            return CombinationStage.AUDIO_PROCESSING

        except Exception as e:
            self.main_app.log_message(f"[TaskFlow] 阶段检测异常: {str(e)}")
            return CombinationStage.AUDIO_PROCESSING

    def _check_merged_subtitles_status(self, task_folder, file_name):
        """检查合并字幕的状态"""
        try:
            expected_combinations = self._get_expected_combinations_count(task_folder, file_name)
            existing_merged_files = self.main_app._find_merged_subtitle_files(task_folder, file_name)

            return {
                'complete': len(existing_merged_files) == expected_combinations and expected_combinations > 0,
                'partial': len(existing_merged_files) > 0,
                'count': len(existing_merged_files),
                'expected': expected_combinations,
                'files': existing_merged_files
            }
        except Exception as e:
            self.main_app.log_message(f"[ERROR] 检查合并字幕状态异常: {e}")
            return {'complete': False, 'partial': False, 'count': 0, 'expected': 0, 'files': []}

    def _check_segment_subtitles_status(self, task_folder, file_name):
        """检查分段字幕的状态"""
        try:
            # 使用现有的SegmentationUtils
            from .segmentation_utils import SegmentationUtils

            segment_files = SegmentationUtils.find_segment_files(task_folder, file_name)
            if not segment_files:
                return {'ready_for_merge': False, 'partial': False}

            enabled_asr_services = self.main_app._get_enabled_asr_services()
            enabled_llm_apis = self.main_app._get_enabled_llm_apis()

            status = SegmentationUtils.analyze_segmented_combinations_status(
                task_folder, segment_files, enabled_asr_services, enabled_llm_apis,
                validate_transcription_func=self.main_app._validate_transcription_file
            )

            return {
                'ready_for_merge': status and status.get('subtitle_complete', False),
                'partial': status and status.get('subtitle_ready', 0) > 0,
                'status': status
            }
        except Exception as e:
            self.main_app.log_message(f"[ERROR] 检查分段字幕状态异常: {e}")
            return {'ready_for_merge': False, 'partial': False}

    def _get_expected_combinations_count(self, task_folder, file_name):
        """获取期望的ASR-LLM组合数量"""
        try:
            enabled_asr_services = self.main_app._get_enabled_asr_services()
            enabled_llm_apis = self.main_app._get_enabled_llm_apis()
            return len(enabled_asr_services) * len(enabled_llm_apis)
        except Exception:
            return 0



    def handle_incremental_processing(self, task_folder: str, file_name: str, current_stage: CombinationStage) -> Dict:
        """增量处理决策逻辑

        根据当前阶段智能决策下一步动作：
        - 已完成：无需处理
        - 字幕生成中：检查转录状态，决定是否生成字幕
        - 转录进行中：检查ASR服务状态，决定转录或解析动作
        - 音频处理中：执行音频转换

        Args:
            task_folder: 任务文件夹路径
            file_name: 原始文件名
            current_stage: 当前处理阶段

        Returns:
            Dict: 决策结果，包含action字段和相关参数
        """
        try:
            if current_stage == CombinationStage.COMPLETED:
                return {'action': 'skip', 'reason': 'already_completed'}

            elif current_stage == CombinationStage.SUBTITLE_PROCESSING:
                # 有转录结果，需要生成字幕
                return {'action': 'generate_subtitles', 'stage': current_stage}

            elif current_stage == CombinationStage.TRANSCRIPTION_PROCESSING:
                # 有音频文件，需要检查转录状态
                asr_status = self._check_asr_services_status(task_folder, file_name)

                # 当ASR状态检查返回check_subtitle_readiness时，进入字幕生成阶段
                if asr_status.get('action') == 'check_subtitle_readiness':
                    if self.debug_mode:
                        self.main_app.log_message("[TaskFlow] ASR服务已完成，准备进入字幕生成阶段")
                    return {'action': 'generate_subtitles', 'stage': CombinationStage.SUBTITLE_PROCESSING}

                # 处理分段解析action
                elif asr_status.get('action') == 'parse_segments':
                    if self.debug_mode:
                        self.main_app.log_message("[TaskFlow] 需要解析分段文件")
                    return asr_status

                # 处理分段转录action
                elif asr_status.get('action') == 'transcribe_segments':
                    if self.debug_mode:
                        self.main_app.log_message("[TaskFlow] 需要转录分段文件")
                    return asr_status

                return asr_status

            else:  # 音频处理阶段
                return {'action': 'process_audio', 'stage': current_stage}

        except Exception as e:
            self.main_app.log_message(f"[TaskFlow] 增量处理决策异常: {str(e)}")
            return {'action': 'fallback', 'error': str(e)}

    def _check_asr_services_status(self, task_folder: str, file_name: str) -> Dict:
        """检查ASR服务状态，决定是否需要转录或解析 - 修复：优先检查分段文件

        这个方法整合了原有的服务状态检查逻辑，统一管理ASR服务的状态检查。
        修复增量服务失效问题：优先检查分段文件的转录状态。

        Args:
            task_folder: 任务文件夹路径
            file_name: 基础文件名

        Returns:
            Dict: 包含处理决策的字典
        """
        try:
            # 获取启用的ASR服务列表
            enabled_services = self.main_app._get_enabled_asr_services()

            if not enabled_services:
                return {'action': 'error', 'reason': 'no_enabled_services'}

            if self.debug_mode:
                self.main_app.log_message(f"[TaskFlow] 检查ASR服务状态: {', '.join(enabled_services)}")

            # 🔧 修复：优先检查分段文件
            from .segmentation_utils import SegmentationUtils

            segment_files = SegmentationUtils.find_segment_files(task_folder, file_name)

            if segment_files:
                if self.debug_mode:
                    self.main_app.log_message(f"[TaskFlow] 发现 {len(segment_files)} 个分段文件，检查分段转录状态")

                # 检查分段文件的转录状态
                return self._check_segmented_asr_status(task_folder, segment_files, enabled_services)
            else:
                if self.debug_mode:
                    self.main_app.log_message(f"[TaskFlow] 未发现分段文件，检查原始文件转录状态")

                # 原有逻辑：检查原始文件
                return self._check_original_file_asr_status(task_folder, file_name, enabled_services)

        except Exception as e:
            self.main_app.log_message(f"[TaskFlow] ASR服务状态检查异常: {str(e)}")
            return {'action': 'error', 'reason': f'exception: {str(e)}'}

    def _check_segmented_asr_status(self, task_folder: str, segment_files: list, enabled_services: list) -> Dict:
        """检查分段文件的ASR转录状态 - 修复：正确处理解析需求

        Args:
            task_folder: 任务文件夹路径
            segment_files: 分段文件列表
            enabled_services: 启用的ASR服务列表

        Returns:
            Dict: 包含处理决策的字典
        """
        try:
            if self.debug_mode:
                self.main_app.log_message(f"[TaskFlow] 检查 {len(segment_files)} 个分段的ASR状态")

            # 分类分段状态
            segments_completed = []      # 已完成的分段
            segments_need_parsing = []   # 需要解析的分段
            segments_need_transcription = []  # 需要转录的分段

            for segment_file in segment_files:
                segment_base = os.path.splitext(os.path.basename(segment_file))[0]

                # 检查该分段的所有启用服务状态
                for service in enabled_services:
                    status = self.main_app._check_service_transcription_status(
                        task_folder, segment_base, service
                    )

                    if status == 'completed':
                        segments_completed.append({
                            'segment': segment_base,
                            'service': service
                        })
                    elif status == 'needs_parsing':
                        segments_need_parsing.append({
                            'segment': segment_base,
                            'service': service
                        })
                    elif status == 'needs_transcription':
                        segments_need_transcription.append({
                            'segment': segment_base,
                            'service': service
                        })

            # 统计结果
            total_combinations = len(segment_files) * len(enabled_services)
            completed_count = len(segments_completed)
            parsing_count = len(segments_need_parsing)
            transcription_count = len(segments_need_transcription)

            if self.debug_mode:
                self.main_app.log_message(f"[TaskFlow] 分段ASR状态统计: 已完成={completed_count}, 需解析={parsing_count}, 需转录={transcription_count}")

            # 决策逻辑：优先级 解析 > 转录 > 字幕生成
            if segments_need_parsing:
                # 有分段需要解析，优先处理解析
                return {
                    'action': 'parse_segments',
                    'reason': 'segments_need_parsing',
                    'segments_need_parsing': segments_need_parsing,
                    'segments_completed': segments_completed,
                    'total_combinations': total_combinations,
                    'is_segmented': True
                }
            elif segments_need_transcription:
                # 有分段需要转录
                return {
                    'action': 'transcribe_segments',
                    'reason': 'segments_need_transcription',
                    'segments_need_transcription': segments_need_transcription,
                    'segments_completed': segments_completed,
                    'total_combinations': total_combinations,
                    'is_segmented': True
                }
            else:
                # 所有分段都已完成（转录+解析），可以进入字幕生成
                return {
                    'action': 'check_subtitle_readiness',
                    'reason': 'all_segments_completed',
                    'segments_completed': segments_completed,
                    'total_combinations': total_combinations,
                    'is_segmented': True
                }

        except Exception as e:
            self.main_app.log_message(f"[TaskFlow] 分段ASR状态检查异常: {str(e)}")
            return {'action': 'error', 'reason': f'segmented_check_exception: {str(e)}'}

    def _check_original_file_asr_status(self, task_folder: str, file_name: str, enabled_services: list) -> Dict:
        """检查原始文件的ASR转录状态（原有逻辑）

        Args:
            task_folder: 任务文件夹路径
            file_name: 基础文件名
            enabled_services: 启用的ASR服务列表

        Returns:
            Dict: 包含处理决策的字典
        """
        try:
            # 分类服务状态
            services_completed = []      # 已完成的服务
            services_need_parsing = []   # 需要解析的服务
            services_need_transcription = []  # 需要转录的服务

            # 检查每个服务的状态
            for service in enabled_services:
                status = self.main_app._check_service_transcription_status(task_folder, file_name, service)

                if status == 'completed':
                    services_completed.append(service)
                elif status == 'needs_parsing':
                    services_need_parsing.append(service)
                elif status == 'needs_transcription':
                    services_need_transcription.append(service)

            # 统计结果
            total_services = len(enabled_services)
            completed_count = len(services_completed)

            if self.debug_mode:
                self.main_app.log_message(f"[TaskFlow] 原始文件ASR状态统计: 已完成={completed_count}, 需解析={len(services_need_parsing)}, 需转录={len(services_need_transcription)}")

            # 决策逻辑
            if services_need_parsing:
                # 有服务需要解析，优先处理解析
                return {
                    'action': 'parse_and_continue',
                    'parse_services': services_need_parsing,
                    'completed_services': services_completed,
                    'total_services': total_services
                }

            elif services_need_transcription:
                # 有服务需要转录
                # 检查API配置
                valid_services = self._filter_valid_api_services(services_need_transcription)

                if valid_services:
                    return {
                        'action': 'transcribe',
                        'transcribe_services': valid_services,
                        'completed_services': services_completed,
                        'total_services': total_services
                    }
                else:
                    # 没有有效的API配置
                    if completed_count > 0:
                        # 有已完成的服务，可以继续
                        return {
                            'action': 'check_subtitle_readiness',
                            'completed_services': services_completed,
                            'total_services': total_services,
                            'reason': 'api_config_invalid_but_has_completed'
                        }
                    else:
                        return {
                            'action': 'error',
                            'reason': 'no_valid_api_config',
                            'invalid_services': services_need_transcription
                        }

            else:
                # 所有服务都已完成，检查是否可以进入字幕生成
                if completed_count > 0:
                    return {
                        'action': 'check_subtitle_readiness',
                        'completed_services': services_completed,
                        'total_services': total_services,
                        'reason': 'all_services_completed'
                    }
                else:
                    return {
                        'action': 'error',
                        'reason': 'no_completed_services'
                    }

        except Exception as e:
            self.main_app.log_message(f"[TaskFlow] 原始文件ASR状态检查异常: {str(e)}")
            return {'action': 'error', 'reason': f'original_file_check_exception: {str(e)}'}

    def _filter_valid_api_services(self, services: List[str]) -> List[str]:
        """过滤出有有效API配置的服务

        Args:
            services: 服务列表

        Returns:
            List[str]: 有效的服务列表
        """
        valid_services = []

        for service in services:
            api_key_valid = False

            if service == 'ElevenLabs':
                api_key_valid = bool(self.main_app.elevenlabs_settings.get('api_key', '').strip())
            elif service == 'AssemblyAI':
                api_key_valid = bool(self.main_app.assemblyai_settings.get('api_key', '').strip())
            elif service == 'Deepgram':
                api_key_valid = bool(self.main_app.deepgram_settings.get('api_key', '').strip())

            if api_key_valid:
                valid_services.append(service)
            elif self.debug_mode:
                self.main_app.log_message(f"[TaskFlow] {service} API配置无效")

        return valid_services

    def _execute_flow_action(self, task: Dict, task_index: int, flow_action: Dict) -> bool:
        """执行流转决策

        Args:
            task: 任务信息
            task_index: 任务索引
            flow_action: 流转决策

        Returns:
            bool: 是否成功执行
        """
        action = flow_action.get('action', 'unknown')

        # 更新统计信息
        self.stats['total_tasks_processed'] += 1

        if action == 'skip':
            self.main_app.log_message(f"[TaskFlow] 跳过已完成的任务")
            self._update_task_stage(task_index, CombinationStage.COMPLETED)
            self.stats['success_count'] += 1
            return True

        elif action == 'generate_subtitles':
            self.main_app.log_message(f"[TaskFlow] 开始字幕生成")
            self._update_task_stage(task_index, CombinationStage.SUBTITLE_PROCESSING)
            return self._execute_subtitle_generation(task, task_index)

        elif action == 'parse_and_continue':
            self.main_app.log_message(f"[TaskFlow] 开始解析并继续处理")
            return self._execute_parse_and_continue(task, task_index, flow_action)

        elif action == 'transcribe':
            self.main_app.log_message(f"[TaskFlow] 开始转录处理")
            self._update_task_stage(task_index, CombinationStage.TRANSCRIPTION_PROCESSING)
            return self._execute_transcription(task, task_index, flow_action)

        elif action == 'parse_segments':
            self.main_app.log_message(f"[TaskFlow] 开始分段解析处理")
            self._update_task_stage(task_index, CombinationStage.TRANSCRIPTION_PROCESSING)
            return self._execute_segmented_parsing(task, task_index, flow_action)

        elif action == 'transcribe_segments':
            self.main_app.log_message(f"[TaskFlow] 开始分段转录处理")
            self._update_task_stage(task_index, CombinationStage.TRANSCRIPTION_PROCESSING)
            return self._execute_segmented_transcription_from_action(task, task_index, flow_action)

        elif action == 'process_audio':
            self.main_app.log_message(f"[TaskFlow] 开始音频处理")
            self._update_task_stage(task_index, CombinationStage.AUDIO_PROCESSING)
            return self._execute_audio_processing(task, task_index)

        elif action == 'error':
            error_reason = flow_action.get('reason', 'unknown')
            self.main_app.log_message(f"[TaskFlow] 处理错误: {error_reason}")
            self.stats['error_count'] += 1
            return False

        else:
            # 未知操作，记录错误
            self.main_app.log_message(f"[TaskFlow] 未知操作: {action}")
            self.stats['error_count'] += 1
            return False

    def trigger_next_stage(self, current_stage: CombinationStage, task: Dict, task_index: int) -> bool:
        """自动触发下一阶段处理

        这个方法实现完整的自动阶段流转机制，根据当前阶段智能决定下一步操作。

        Args:
            current_stage: 当前阶段
            task: 任务信息
            task_index: 任务索引

        Returns:
            bool: 是否成功触发下一阶段
        """
        try:
            if self.debug_mode:
                self.main_app.log_message(f"[TaskFlow] 触发下一阶段: {current_stage.display_text}")

            input_file = task.get('path', '')
            task_folder = self.main_app._get_task_project_folder(input_file)
            file_name = os.path.splitext(os.path.basename(input_file))[0]

            if current_stage == CombinationStage.AUDIO_PROCESSING:
                # 音频处理完成，检查是否可以进入转录阶段
                if self.main_app._find_any_audio_file(task_folder, file_name):
                    self.main_app.log_message("[TaskFlow] 音频处理完成，进入转录阶段")
                    self._update_task_stage(task_index, CombinationStage.TRANSCRIPTION_PROCESSING)
                    return self._trigger_transcription_stage(task, task_index)
                else:
                    self.main_app.log_message("[TaskFlow] 音频文件不存在，无法进入转录阶段")
                    return False

            elif current_stage == CombinationStage.TRANSCRIPTION_PROCESSING:
                # 转录处理完成，检查是否可以进入字幕生成阶段
                if self.main_app._find_existing_transcriptions(task_folder, file_name):
                    self.main_app.log_message("[TaskFlow] 转录完成，进入字幕生成阶段")
                    self._update_task_stage(task_index, CombinationStage.SUBTITLE_PROCESSING)
                    return self._execute_subtitle_generation(task, task_index)
                else:
                    self.main_app.log_message("[TaskFlow] 转录结果不存在，无法进入字幕生成阶段")
                    return False

            elif current_stage == CombinationStage.SUBTITLE_PROCESSING:
                # 字幕生成完成，任务完成
                self.main_app.log_message("[TaskFlow] 字幕生成完成，任务完成")
                self._update_task_stage(task_index, CombinationStage.COMPLETED)
                return True

            elif current_stage == CombinationStage.COMPLETED:
                # 任务已完成，无需进一步处理
                self.main_app.log_message("[TaskFlow] 任务已完成")
                return True

            else:
                # 未知阶段或失败状态
                self.main_app.log_message(f"[TaskFlow] 未知阶段或失败状态: {current_stage}")
                return False

        except Exception as e:
            self.main_app.log_message(f"[TaskFlow] 触发下一阶段异常: {str(e)}")
            return False

    def _trigger_transcription_stage(self, task: Dict, task_index: int) -> bool:
        """触发转录阶段处理 - 纯转录逻辑，不处理音频分割

        音频分割已在音频处理阶段完成，此方法只负责转录相关处理。

        Args:
            task: 任务信息
            task_index: 任务索引

        Returns:
            bool: 是否成功触发
        """
        try:
            input_file = task.get('path', '')
            task_folder = self.main_app._get_task_project_folder(input_file)
            file_name = os.path.splitext(os.path.basename(input_file))[0]

            # 检查ASR服务状态
            asr_status = self._check_asr_services_status(task_folder, file_name)

            if asr_status.get('action') == 'parse_and_continue':
                # 需要解析
                return self._execute_parse_and_continue(task, task_index, asr_status)
            elif asr_status.get('action') == 'transcribe':
                # 需要转录
                return self._execute_transcription(task, task_index, asr_status)
            elif asr_status.get('action') == 'check_subtitle_readiness':
                # 转录已完成，可以进入字幕生成
                return self.trigger_next_stage(CombinationStage.TRANSCRIPTION_PROCESSING, task, task_index)
            else:
                # 错误或其他情况
                self.main_app.log_message(f"[TaskFlow] 转录阶段处理失败: {asr_status.get('reason', 'unknown')}")
                return False

        except Exception as e:
            self.main_app.log_message(f"[TaskFlow] 触发转录阶段异常: {str(e)}")
            return False

    def _should_segment_audio(self, audio_file: str) -> bool:
        """判断是否需要对音频进行分割

        Args:
            audio_file: 音频文件路径

        Returns:
            bool: 是否需要分割
        """
        try:
            # 获取音频时长
            from services.segmentation.segmentation_service import AudioSegmentationService
            service = AudioSegmentationService()
            duration = service.get_media_duration(audio_file)

            if duration is None:
                self.main_app.log_message(f"[TaskFlow] 无法获取音频时长，跳过分割: {os.path.basename(audio_file)}")
                return False

            # 分割阈值：使用分割间隔作为阈值
            threshold_seconds = service.INTERVAL

            if duration > threshold_seconds:
                minutes = duration / 60
                self.main_app.log_message(f"[TaskFlow] 音频时长 {minutes:.1f} 分钟，超过阈值 {threshold_seconds/60} 分钟，需要分割")
                return True
            else:
                minutes = duration / 60
                self.main_app.log_message(f"[TaskFlow] 音频时长 {minutes:.1f} 分钟，无需分割")
                return False

        except Exception as e:
            self.main_app.log_message(f"[TaskFlow] 判断分割需求异常: {str(e)}")
            return False

    def _execute_segmented_transcription_flow(self, task: Dict, task_index: int, audio_file: str) -> bool:
        """执行分段转录流程

        Args:
            task: 任务信息
            task_index: 任务索引
            audio_file: 音频文件路径

        Returns:
            bool: 是否成功
        """
        try:
            task_folder = os.path.dirname(audio_file)

            # 1. 执行音频分割
            segmentation_result = self._execute_audio_segmentation(audio_file)

            if not segmentation_result.success:
                self.main_app.log_message(f"[TaskFlow] 音频分割失败: {segmentation_result.error_message}")
                return False

            self.main_app.log_message(f"[TaskFlow] 音频分割成功，生成 {len(segmentation_result.segment_files)} 个分段")

            # 2. 执行分段转录
            return self._execute_segmented_transcription_from_result(task, task_index, segmentation_result)

        except Exception as e:
            self.main_app.log_message(f"[TaskFlow] 分段转录流程异常: {str(e)}")
            return False

    def _execute_audio_segmentation(self, audio_file: str):
        """执行音频分割

        Args:
            audio_file: 音频文件路径

        Returns:
            SegmentationResult: 分割结果
        """
        try:
            from services.segmentation.segmentation_service import AudioSegmentationService

            # 创建分割服务
            service = AudioSegmentationService()

            # 输出目录：音频文件同一目录（遵循code.py逻辑）
            output_dir = os.path.dirname(audio_file)

            self.main_app.log_message(f"[TaskFlow] 开始音频分割: {os.path.basename(audio_file)}")

            # 执行分割
            result = service.segment_audio(audio_file, output_dir)

            if result.success:
                self.main_app.log_message(f"[TaskFlow] 分割完成: {len(result.segment_files)} 个分段，耗时 {result.processing_time_seconds:.2f}s")
            else:
                self.main_app.log_message(f"[TaskFlow] 分割失败: {result.error_message}")

            return result

        except Exception as e:
            self.main_app.log_message(f"[TaskFlow] 音频分割异常: {str(e)}")
            # 返回失败结果
            from services.segmentation.segmentation_service import SegmentationResult
            return SegmentationResult(
                success=False,
                input_path=audio_file,
                error_message=str(e)
            )

    def _execute_segmented_transcription_from_result(self, task: Dict, task_index: int, segmentation_result) -> bool:
        """执行分段转录 - 从分割结果直接处理

        Args:
            task: 任务信息
            task_index: 任务索引
            segmentation_result: 分割结果（SegmentationResult对象）

        Returns:
            bool: 是否成功
        """
        try:
            # 修复：在开始分段转录前，更新任务阶段到转录阶段
            self.main_app.log_message("[TaskFlow] 音频分割完成，进入转录阶段")
            self._update_task_stage(task_index, CombinationStage.TRANSCRIPTION_PROCESSING)

            # 获取原始文件信息
            original_file_path = task.get('path', '')

            # 获取启用的ASR服务
            enabled_services = self.main_app._get_enabled_asr_services()

            if not enabled_services:
                self.main_app.log_message("[TaskFlow] 没有启用的ASR服务")
                return False

            task_folder = os.path.dirname(segmentation_result.segment_files[0])

            self.main_app.log_message(f"[TaskFlow] 开始分段转录，启用服务: {', '.join(enabled_services)}")

            # 对每个启用的ASR服务（顺序处理）
            for service in enabled_services:
                self.main_app.log_message(f"[TaskFlow] 处理ASR服务: {service}")

                # 对每个分段文件（顺序处理）
                for i, segment_file in enumerate(segmentation_result.segment_files):
                    segment_base = os.path.splitext(os.path.basename(segment_file))[0]

                    self.main_app.log_message(f"[TaskFlow] 处理分段 {i+1}/{len(segmentation_result.segment_files)}: {segment_base}")

                    # 检查分段状态
                    status = self.main_app._check_service_transcription_status(
                        task_folder, segment_base, service
                    )

                    if status == 'needs_transcription':
                        # 转录分段
                        success = self._transcribe_segment(segment_file, service, original_file_path)
                        if not success:
                            raise Exception(f"分段转录失败: {segment_base} - {service}")

                    elif status == 'needs_parsing':
                        # 解析分段
                        success = self._parse_segment_json(task_folder, segment_base, service)
                        if not success:
                            raise Exception(f"分段解析失败: {segment_base} - {service}")

                    elif status == 'completed':
                        self.main_app.log_message(f"[TaskFlow] 分段已完成: {segment_base} - {service}")

            # 检查所有分段ASR是否完成
            if self._all_segments_asr_completed(segmentation_result, enabled_services):
                self.main_app.log_message("[TaskFlow] 所有分段ASR处理完成，触发LLM阶段")
                return self._trigger_segmented_llm_stage(task, task_index, segmentation_result)
            else:
                self.main_app.log_message("[TaskFlow] 部分分段ASR未完成")
                return False

        except Exception as e:
            # 任何失败都直接报错并中止
            self.main_app.log_message(f"[TaskFlow] 分段转录失败，中止操作: {str(e)}")
            return False

    def _transcribe_segment(self, segment_file: str, service: str, original_file_path: str) -> bool:
        """转录单个分段 - 使用现有转录方法

        Args:
            segment_file: 分段文件路径
            service: ASR服务名称
            original_file_path: 原始文件路径（用于日志）

        Returns:
            bool: 是否成功
        """
        try:
            segment_base = os.path.splitext(os.path.basename(segment_file))[0]

            self.main_app.log_message(f"[TaskFlow] 开始转录分段: {segment_base} - {service}")

            # 根据服务类型调用对应的转录方法
            if service == 'ElevenLabs':
                success = self.main_app._run_elevenlabs_transcription(
                    segment_file,    # 音频文件路径
                    segment_file,    # 原始文件路径（使用分段文件本身）
                    segment_base     # 任务名称（使用分段基础名）
                )
            elif service == 'AssemblyAI':
                success = self.main_app._run_assemblyai_transcription(
                    segment_file,    # 音频文件路径
                    segment_file,    # 原始文件路径
                    segment_base     # 任务名称
                )
            elif service == 'Deepgram':
                success = self.main_app._run_deepgram_transcription(
                    segment_file,    # 音频文件路径
                    segment_file,    # 原始文件路径
                    segment_base     # 任务名称
                )
            else:
                self.main_app.log_message(f"[TaskFlow] 未知的ASR服务: {service}")
                return False

            # 转录成功后立即解析（与整体转录流程保持一致）
            if success:
                self.main_app.log_message(f"[TaskFlow] 分段转录成功: {segment_base} - {service}")

                # 立即解析转录结果
                task_folder = os.path.dirname(segment_file)
                self.main_app.log_message(f"[TaskFlow] 开始解析分段结果: {segment_base} - {service}")

                parse_success = self.main_app._parse_service_json(task_folder, segment_base, service)

                if parse_success:
                    self.main_app.log_message(f"[TaskFlow] 分段转录和解析完成: {segment_base} - {service}")
                    return True
                else:
                    self.main_app.log_message(f"[TaskFlow] 分段转录成功但解析失败: {segment_base} - {service}")
                    # 检查JSON文件是否存在，提供更详细的错误信息
                    json_file = os.path.join(task_folder, f"{segment_base}-{service}.json")
                    if os.path.exists(json_file):
                        self.main_app.log_message(f"[TaskFlow] JSON文件存在但解析失败，请检查文件格式: {json_file}")
                    else:
                        self.main_app.log_message(f"[TaskFlow] JSON文件不存在: {json_file}")
                    return False  # 解析失败视为整体失败
            else:
                self.main_app.log_message(f"[TaskFlow] 分段转录失败: {segment_base} - {service}")
                return False

        except Exception as e:
            self.main_app.log_message(f"[TaskFlow] 分段转录异常: {str(e)}")
            return False

    def _parse_segment_json(self, task_folder: str, segment_base: str, service: str) -> bool:
        """解析分段JSON文件 - 使用现有解析方法

        Args:
            task_folder: 任务文件夹
            segment_base: 分段基础名称
            service: ASR服务名称

        Returns:
            bool: 是否成功
        """
        try:
            self.main_app.log_message(f"[TaskFlow] 开始解析分段JSON: {segment_base} - {service}")

            # 直接调用现有的JSON解析方法
            success = self.main_app._parse_service_json(task_folder, segment_base, service)

            if success:
                self.main_app.log_message(f"[TaskFlow] 分段JSON解析成功: {segment_base} - {service}")
            else:
                self.main_app.log_message(f"[TaskFlow] 分段JSON解析失败: {segment_base} - {service}")

            return success

        except Exception as e:
            self.main_app.log_message(f"[TaskFlow] 分段JSON解析异常: {str(e)}")
            return False

    def _all_segments_asr_completed(self, segmentation_result, enabled_services) -> bool:
        """检查所有分段的ASR是否完成 - 支持最多2个失败组合的容忍度

        Args:
            segmentation_result: 分割结果
            enabled_services: 启用的ASR服务列表

        Returns:
            bool: 是否达到完成要求（失败组合数≤2）
        """
        try:
            task_folder = os.path.dirname(segmentation_result.segment_files[0])

            total_combinations = 0
            completed_combinations = 0
            failed_combinations = []

            for segment_file in segmentation_result.segment_files:
                segment_base = os.path.splitext(os.path.basename(segment_file))[0]

                for service in enabled_services:
                    total_combinations += 1
                    status = self.main_app._check_service_transcription_status(
                        task_folder, segment_base, service
                    )

                    if status == 'completed':
                        completed_combinations += 1
                    else:
                        failed_combinations.append(f"{segment_base}-{service}({status})")

            # 统计失败个数
            failed_count = len(failed_combinations)

            # 最多允许2个失败组合
            MAX_ALLOWED_FAILURES = 2

            if failed_count <= MAX_ALLOWED_FAILURES:
                if failed_count > 0:
                    self.main_app.log_message(f"[TaskFlow] 分段ASR有 {failed_count} 个失败组合，在允许范围内(≤{MAX_ALLOWED_FAILURES})，允许进入下一阶段")
                    self.main_app.log_message(f"[TaskFlow] 失败的组合: {', '.join(failed_combinations)}")
                else:
                    self.main_app.log_message(f"[TaskFlow] 所有分段ASR完成: {completed_combinations}/{total_combinations}")
                return True
            else:
                self.main_app.log_message(f"[TaskFlow] 分段ASR失败组合过多: {failed_count} 个，超过允许上限(≤{MAX_ALLOWED_FAILURES})")
                self.main_app.log_message(f"[TaskFlow] 失败的组合: {', '.join(failed_combinations)}")
                return False

        except Exception as e:
            self.main_app.log_message(f"[TaskFlow] 检查分段ASR状态异常: {str(e)}")
            return False

    def _trigger_segmented_llm_stage(self, task: Dict, task_index: int, segmentation_result) -> bool:
        """触发分段LLM处理阶段

        Args:
            task: 任务信息
            task_index: 任务索引
            segmentation_result: 分割结果

        Returns:
            bool: 是否成功
        """
        try:
            enabled_asr_services = self.main_app._get_enabled_asr_services()
            enabled_llm_apis = self.main_app._get_enabled_llm_apis()
            task_folder = os.path.dirname(segmentation_result.segment_files[0])

            self.main_app.log_message(f"[TaskFlow] 开始分段LLM处理，ASR服务: {', '.join(enabled_asr_services)}, LLM API: {', '.join(enabled_llm_apis)}")

            # 对每个ASR-LLM组合（顺序处理）
            for asr_service in enabled_asr_services:
                for llm_api in enabled_llm_apis:
                    self.main_app.log_message(f"[TaskFlow] 处理组合: {asr_service}-{llm_api}")

                    # 对每个分段（顺序处理）
                    for i, segment_file in enumerate(segmentation_result.segment_files):
                        segment_base = os.path.splitext(os.path.basename(segment_file))[0]

                        self.main_app.log_message(f"[TaskFlow] 处理分段字幕 {i+1}/{len(segmentation_result.segment_files)}: {segment_base}")

                        # 检查分段字幕状态
                        subtitle_file = f"{segment_base}-{asr_service}-{llm_api}.srt"
                        subtitle_path = os.path.join(task_folder, subtitle_file)

                        if not os.path.exists(subtitle_path):
                            # 生成分段字幕 - 失败直接抛出异常
                            success = self._generate_segment_subtitle(
                                task_folder, segment_base, asr_service, llm_api
                            )
                            if not success:
                                raise Exception(f"分段字幕生成失败: {segment_base} - {asr_service}-{llm_api}")
                        else:
                            self.main_app.log_message(f"[TaskFlow] 分段字幕已存在: {subtitle_file}")

            self.main_app.log_message("[TaskFlow] 所有分段LLM处理完成")

            # 🔧 关键修复：不直接返回True，而是重新触发增量处理
            # 让TaskFlowController重新检测当前阶段，继续增量处理循环
            input_file = task.get('path', '')
            task_folder = self.main_app._get_task_project_folder(input_file)
            file_name = os.path.splitext(os.path.basename(input_file))[0]

            # 重新检测当前阶段（应该检测到需要合并复制）
            current_stage = self.detect_current_stage(task_folder, file_name)

            if current_stage == CombinationStage.SUBTITLE_PROCESSING:
                # 进入字幕生成阶段（包括合并复制）
                self.main_app.log_message("[TaskFlow] 分段完成，继续字幕合并复制流程")
                return self._execute_subtitle_generation(task, task_index)
            elif current_stage == CombinationStage.COMPLETED:
                # 已经完成（可能是降级处理）
                self.main_app.log_message("[TaskFlow] 任务已完成")
                return True
            else:
                # 其他情况，继续增量处理
                self.main_app.log_message(f"[TaskFlow] 分段完成，检测到阶段: {current_stage.display_text}，继续增量处理")
                flow_action = self.handle_incremental_processing(task_folder, file_name, current_stage)
                return self._execute_flow_action(task, task_index, flow_action)

        except Exception as e:
            # 任何失败都直接报错并中止
            self.main_app.log_message(f"[TaskFlow] 分段LLM处理失败，中止操作: {str(e)}")
            return False

    def _generate_segment_subtitle(self, task_folder: str, segment_base: str,
                                 asr_service: str, llm_api: str) -> bool:
        """生成分段字幕 - 使用现有字幕生成流程

        Args:
            task_folder: 任务文件夹
            segment_base: 分段基础名称
            asr_service: ASR服务名称
            llm_api: LLM API名称

        Returns:
            bool: 是否成功
        """
        try:
            self.main_app.log_message(f"[TaskFlow] 开始生成分段字幕: {segment_base} - {asr_service}-{llm_api}")

            # 1. 检查parsed.json文件是否存在
            parsed_file = f"{segment_base}-{asr_service}-parsed.json"
            parsed_path = os.path.join(task_folder, parsed_file)

            if not os.path.exists(parsed_path):
                self.main_app.log_message(f"[TaskFlow] parsed文件不存在: {parsed_file}")
                return False

            # 2. 使用现有的LLM服务生成字幕
            from services.subtitle.llm_service import SubtitleLLMService
            subtitle_service = SubtitleLLMService()

            # 3. 调用字幕生成（完全照搬现有逻辑）
            result = subtitle_service.generate_subtitle(parsed_path)

            # 4. 检查结果
            if result.get('success', False):
                self.main_app.log_message(f"[TaskFlow] 分段字幕生成成功: {segment_base} - {asr_service}-{llm_api}")
                return True
            else:
                error_msg = result.get('error', '未知错误')
                self.main_app.log_message(f"[TaskFlow] 分段字幕生成失败: {segment_base} - {asr_service}-{llm_api} - {error_msg}")
                return False

        except Exception as e:
            self.main_app.log_message(f"[TaskFlow] 分段字幕生成异常: {str(e)}")
            return False

    def _update_task_stage(self, task_index: int, new_stage: CombinationStage, custom_status: str = None):
        """统一的任务阶段更新方法

        这个方法确保TaskFlowController与EISA架构的完美集成，
        提供统一的状态同步机制。

        Args:
            task_index: 任务索引
            new_stage: 新的阶段
            custom_status: 自定义状态信息（可选）
        """
        try:
            if not (0 <= task_index < len(self.main_app.task_list)):
                self.main_app.log_message(f"[TaskFlow] 无效的任务索引: {task_index}")
                return

            task = self.main_app.task_list[task_index]

            # 智能推断previous stage，避免显示"unknown"
            if 'current_stage' not in task:
                # 根据即将进入的阶段，推断合理的previous stage
                stage_mapping = {
                    CombinationStage.AUDIO_PROCESSING: 'audio_processing',
                    CombinationStage.TRANSCRIPTION_PROCESSING: 'transcription',
                    CombinationStage.SUBTITLE_PROCESSING: 'subtitle_generation',
                    CombinationStage.COMPLETED: 'completed',
                    CombinationStage.FAILED: 'failed'
                }

                # 推断previous stage的逻辑
                if new_stage == CombinationStage.TRANSCRIPTION_PROCESSING:
                    old_stage = 'audio_processing'  # 从音频处理进入转录
                elif new_stage == CombinationStage.SUBTITLE_PROCESSING:
                    old_stage = 'transcription'     # 从转录进入字幕生成
                elif new_stage == CombinationStage.COMPLETED:
                    old_stage = 'subtitle_generation'  # 从字幕生成进入完成
                else:
                    old_stage = 'starting'  # 任务开始状态
            else:
                old_stage = task.get('current_stage', 'unknown')

            # 阶段映射表
            stage_mapping = {
                CombinationStage.AUDIO_PROCESSING: 'audio_processing',
                CombinationStage.TRANSCRIPTION_PROCESSING: 'transcription',
                CombinationStage.SUBTITLE_PROCESSING: 'subtitle_generation',
                CombinationStage.COMPLETED: 'completed',
                CombinationStage.FAILED: 'failed'
            }

            if new_stage not in stage_mapping:
                self.main_app.log_message(f"[TaskFlow] 未知的阶段: {new_stage}")
                return

            new_stage_str = stage_mapping[new_stage]

            # 更新明确阶段字段
            task['current_stage'] = new_stage_str

            # 记录阶段变更
            if self.debug_mode or old_stage != new_stage_str:
                self.main_app.log_message(f"[TaskFlow] 阶段更新: {old_stage} -> {new_stage_str}")

            # 修复：同时更新status字段，确保状态一致性
            if custom_status is None:
                # 为所有阶段设置对应的状态文本，确保current_stage和status字段一致
                task['status'] = new_stage.display_text

            # 如果有自定义状态，也更新它
            if custom_status:
                task['custom_status'] = custom_status
                if self.debug_mode:
                    self.main_app.log_message(f"[TaskFlow] 自定义状态: {custom_status}")

            # 触发EISA进度重新计算
            # 这确保了TaskFlowController的状态更新与EISA架构完全同步
            self.main_app._update_task_progress(task_index, custom_status)

            # 修复：主动更新取消按钮状态
            # 确保TaskFlowController设置阶段后，取消按钮状态能及时响应
            self.main_app._update_cancel_button_state()

            # 更新统计信息
            if new_stage == CombinationStage.COMPLETED:
                self.stats['success_count'] += 1
            elif new_stage == CombinationStage.FAILED:
                self.stats['error_count'] += 1

        except Exception as e:
            self.main_app.log_message(f"[TaskFlow] 更新任务阶段异常: {str(e)}")
            self.stats['error_count'] += 1

    def get_task_stage_info(self, task_index: int) -> Dict:
        """获取任务阶段信息

        Args:
            task_index: 任务索引

        Returns:
            Dict: 包含阶段信息的字典
        """
        try:
            if not (0 <= task_index < len(self.main_app.task_list)):
                return {'error': 'invalid_index'}

            task = self.main_app.task_list[task_index]

            # 修复：传递任务对象而不是索引给EISA计算器
            progress = self.progress_calculator.calculate_progress(task)

            # 修复：正确比较阶段同步状态
            current_stage = task.get('current_stage', 'unknown')
            eisa_stage_id = progress.stage.stage_id if hasattr(progress.stage, 'stage_id') else str(progress.stage)
            is_synced = current_stage == eisa_stage_id

            return {
                'current_stage': current_stage,
                'eisa_stage': progress.stage,
                'progress_percentage': progress.progress_percent,  # 修复：使用正确的属性名
                'status_text': progress.detail_text,              # 修复：使用正确的属性名
                'custom_status': task.get('custom_status'),
                'is_synced': is_synced
            }

        except Exception as e:
            self.main_app.log_message(f"[TaskFlow] 获取任务阶段信息异常: {str(e)}")
            return {'error': str(e)}

    def sync_all_task_stages(self):
        """同步所有任务的阶段状态

        修复后的逻辑：TaskFlowController为主导，EISA为计算引擎。
        不再让EISA覆盖TaskFlowController的设置，而是确保EISA尊重明确设置的阶段。
        """
        try:
            if self.debug_mode:
                self.main_app.log_message("[TaskFlow] 开始检查任务阶段一致性")

            checked_count = 0
            inconsistent_count = 0

            for i in range(len(self.main_app.task_list)):
                try:
                    task = self.main_app.task_list[i]

                    # 检查任务是否有明确的阶段设置
                    if 'current_stage' in task:
                        # 有明确阶段：验证EISA是否正确识别
                        stage_info = self.get_task_stage_info(i)

                        if 'error' not in stage_info:
                            current_stage = task['current_stage']
                            eisa_stage = stage_info['eisa_stage']

                            # 检查是否一致（注意：需要比较stage_id）
                            eisa_stage_str = eisa_stage.stage_id if hasattr(eisa_stage, 'stage_id') else str(eisa_stage)

                            if current_stage != eisa_stage_str:
                                inconsistent_count += 1
                                if self.debug_mode:
                                    self.main_app.log_message(f"[TaskFlow] 任务 {i} 阶段不一致: TaskFlow='{current_stage}', EISA='{eisa_stage_str}'")
                                    self.main_app.log_message(f"[TaskFlow] 保持TaskFlowController设置: '{current_stage}'")

                            checked_count += 1
                    else:
                        # 没有明确阶段：这是正常情况，EISA会基于文件状态计算
                        if self.debug_mode:
                            self.main_app.log_message(f"[TaskFlow] 任务 {i} 没有明确阶段，依赖EISA文件状态检测")

                except Exception as e:
                    self.main_app.log_message(f"[TaskFlow] 检查任务 {i} 异常: {str(e)}")

            if self.debug_mode:
                self.main_app.log_message(f"[TaskFlow] 阶段一致性检查完成: 检查 {checked_count}, 不一致 {inconsistent_count}")
                if inconsistent_count > 0:
                    self.main_app.log_message(f"[TaskFlow] 注意: {inconsistent_count} 个任务存在阶段不一致，已保持TaskFlowController设置")

        except Exception as e:
            self.main_app.log_message(f"[TaskFlow] 检查任务阶段一致性异常: {str(e)}")

    def _execute_parse_and_continue(self, task: Dict, task_index: int, flow_action: Dict) -> bool:
        """执行解析并继续处理

        这是修复核心问题的关键方法：确保解析完成后自动进入字幕生成阶段。

        Args:
            task: 任务信息
            task_index: 任务索引
            flow_action: 包含解析服务列表的流转决策

        Returns:
            bool: 是否成功执行
        """
        try:
            input_file = task.get('path', '')
            task_folder = self.main_app._get_task_project_folder(input_file)
            file_name = os.path.splitext(os.path.basename(input_file))[0]

            parse_services = flow_action.get('parse_services', [])

            if self.debug_mode:
                self.main_app.log_message(f"[TaskFlow] 开始解析服务: {', '.join(parse_services)}")

            # 执行解析
            successfully_parsed = []
            for service in parse_services:
                self.main_app.log_message(f"[TaskFlow] 解析 {service} JSON文件")
                if self.main_app._parse_service_json(task_folder, file_name, service):
                    successfully_parsed.append(service)
                    self.main_app.log_message(f"[TaskFlow] {service} 解析成功")
                else:
                    self.main_app.log_message(f"[TaskFlow] {service} 解析失败")

            # 关键修复：解析完成后，检查是否可以进入字幕生成阶段
            if successfully_parsed:
                self.main_app.log_message(f"[TaskFlow] 解析完成，检查字幕生成条件")

                # 重新检查当前阶段
                current_stage = self.detect_current_stage(task_folder, file_name)

                if current_stage == CombinationStage.SUBTITLE_PROCESSING:
                    # 可以进入字幕生成阶段
                    self.main_app.log_message(f"[TaskFlow] 解析完成，自动进入字幕生成阶段")
                    self._update_task_stage(task_index, CombinationStage.SUBTITLE_PROCESSING)
                    return self._execute_subtitle_generation(task, task_index)
                else:
                    # 还需要更多的转录服务
                    self.main_app.log_message(f"[TaskFlow] 解析完成，但仍需更多转录服务")
                    self._update_task_stage(task_index, CombinationStage.TRANSCRIPTION_PROCESSING)
                    return True
            else:
                # 解析失败，需要重新转录
                self.main_app.log_message(f"[TaskFlow] 所有解析都失败，回退到转录逻辑")
                return self._fallback_to_original_logic(task, task_index)

        except Exception as e:
            self.main_app.log_message(f"[TaskFlow] 解析并继续处理异常: {str(e)}")
            return self._fallback_to_original_logic(task, task_index)

    def _execute_subtitle_generation(self, task: Dict, task_index: int) -> bool:
        """执行字幕生成

        Args:
            task: 任务信息
            task_index: 任务索引

        Returns:
            bool: 是否成功执行
        """
        try:
            if self.debug_mode:
                self.main_app.log_message("[TaskFlow] 开始执行字幕生成")

            # 调用现有的字幕生成方法
            success = self.main_app._generate_subtitles_sync(task, task_index)

            if success:
                self.main_app.log_message("[TaskFlow] 字幕生成成功")
                # 任务完成状态由_generate_subtitles_sync负责标记，避免双重标记问题
                self.stats['success_count'] += 1
            else:
                self.main_app.log_message("[TaskFlow] 字幕生成失败")
                self.stats['error_count'] += 1

            return success

        except Exception as e:
            self.main_app.log_message(f"[TaskFlow] 字幕生成异常: {str(e)}")
            self.stats['error_count'] += 1
            return False

    def _execute_transcription(self, task: Dict, task_index: int, flow_action: Dict) -> bool:
        """执行转录处理

        Args:
            task: 任务信息
            task_index: 任务索引
            flow_action: 包含转录服务列表的流转决策

        Returns:
            bool: 是否成功执行
        """
        try:
            if self.debug_mode:
                self.main_app.log_message("[TaskFlow] 开始执行转录处理")

            input_file = task.get('path', '')
            task_folder = self.main_app._get_task_project_folder(input_file)
            file_name = os.path.splitext(os.path.basename(input_file))[0]

            # 获取需要转录的服务
            transcribe_services = flow_action.get('transcribe_services', [])

            if not transcribe_services:
                self.main_app.log_message("[TaskFlow] 没有需要转录的服务")
                return False

            # 找到音频文件
            audio_file = self.main_app._find_any_audio_file(task_folder, file_name)
            if not audio_file:
                self.main_app.log_message("[TaskFlow] 找不到音频文件")
                return False

            self.main_app.log_message(f"[TaskFlow] 开始转录服务: {', '.join(transcribe_services)}")

            # 执行转录
            successful_services = []
            failed_services = []

            for service in transcribe_services:
                try:
                    success = False

                    if service == 'ElevenLabs':
                        success = self.main_app._run_elevenlabs_transcription(
                            audio_file, input_file, task.get('name', 'unknown')
                        )
                    elif service == 'AssemblyAI':
                        success = self.main_app._run_assemblyai_transcription(
                            audio_file, input_file, task.get('name', 'unknown')
                        )
                    elif service == 'Deepgram':
                        success = self.main_app._run_deepgram_transcription(
                            audio_file, input_file, task.get('name', 'unknown')
                        )

                    if success:
                        self.main_app.log_message(f"[TaskFlow] {service} 转录成功")
                        successful_services.append(service)
                    else:
                        self.main_app.log_message(f"[TaskFlow] {service} 转录失败")
                        failed_services.append(service)

                except Exception as e:
                    self.main_app.log_message(f"[TaskFlow] {service} 转录异常: {str(e)}")
                    failed_services.append(service)

            # 处理转录结果
            if successful_services:
                self.main_app.log_message(f"[TaskFlow] 转录完成，开始解析结果")

                # 解析转录结果
                parsed_services = self.main_app._process_transcription_results(
                    task_folder, file_name, successful_services
                )

                if parsed_services:
                    self.main_app.log_message(f"[TaskFlow] 解析成功，检查是否可以进入字幕生成")

                    # 检查是否可以进入字幕生成阶段
                    if self.main_app._find_existing_transcriptions(task_folder, file_name):
                        # 自动触发下一阶段
                        return self.trigger_next_stage(CombinationStage.TRANSCRIPTION_PROCESSING, task, task_index)
                    else:
                        self.main_app.log_message("[TaskFlow] 转录结果不足，保持当前阶段")
                        self._update_task_stage(task_index, CombinationStage.TRANSCRIPTION_PROCESSING)
                        return True
                else:
                    self.main_app.log_message("[TaskFlow] 解析失败")
                    return False
            else:
                self.main_app.log_message("[TaskFlow] 所有转录服务都失败")
                self.stats['error_count'] += 1
                return False

        except Exception as e:
            self.main_app.log_message(f"[TaskFlow] 转录处理异常: {str(e)}")
            self.stats['error_count'] += 1
            return False

    def _execute_segmented_parsing(self, task: Dict, task_index: int, flow_action: Dict) -> bool:
        """执行分段解析处理

        Args:
            task: 任务信息
            task_index: 任务索引
            flow_action: 包含分段解析信息的流转决策

        Returns:
            bool: 是否成功执行
        """
        try:
            if self.debug_mode:
                self.main_app.log_message("[TaskFlow] 开始执行分段解析处理")

            input_file = task.get('path', '')
            task_folder = self.main_app._get_task_project_folder(input_file)

            # 获取需要解析的分段信息
            segments_need_parsing = flow_action.get('segments_need_parsing', [])

            if not segments_need_parsing:
                self.main_app.log_message("[TaskFlow] 没有需要解析的分段")
                # 检查是否可以进入字幕生成阶段
                return self.trigger_next_stage(CombinationStage.TRANSCRIPTION_PROCESSING, task, task_index)

            self.main_app.log_message(f"[TaskFlow] 需要解析 {len(segments_need_parsing)} 个分段")

            # 执行分段解析
            successful_parsing = 0
            total_parsing = len(segments_need_parsing)

            for item in segments_need_parsing:
                segment_name = item['segment']
                service = item['service']

                try:
                    self.main_app.log_message(f"[TaskFlow] 解析分段: {segment_name} - {service}")

                    success = self.main_app._parse_service_json(task_folder, segment_name, service)

                    if success:
                        self.main_app.log_message(f"[TaskFlow] 分段解析成功: {segment_name} - {service}")
                        successful_parsing += 1
                    else:
                        self.main_app.log_message(f"[TaskFlow] 分段解析失败: {segment_name} - {service}")

                except Exception as e:
                    self.main_app.log_message(f"[TaskFlow] 分段解析异常: {segment_name} - {service}: {str(e)}")

            # 检查解析结果
            if successful_parsing > 0:
                self.main_app.log_message(f"[TaskFlow] 分段解析完成: {successful_parsing}/{total_parsing}")

                # 重新检查分段状态，看是否可以进入字幕生成
                file_name = os.path.splitext(os.path.basename(input_file))[0]
                from .segmentation_utils import SegmentationUtils
                segment_files = SegmentationUtils.find_segment_files(task_folder, file_name)

                if segment_files:
                    enabled_services = self.main_app._get_enabled_asr_services()
                    status = self._check_segmented_asr_status(task_folder, segment_files, enabled_services)

                    if status.get('action') == 'check_subtitle_readiness':
                        self.main_app.log_message("[TaskFlow] 所有分段解析完成，进入字幕生成阶段")
                        return self.trigger_next_stage(CombinationStage.TRANSCRIPTION_PROCESSING, task, task_index)
                    else:
                        self.main_app.log_message("[TaskFlow] 部分分段仍需处理")
                        return False
                else:
                    self.main_app.log_message("[TaskFlow] 分段解析完成，但无法检查状态")
                    return False
            else:
                self.main_app.log_message("[TaskFlow] 所有分段解析都失败")
                self.stats['error_count'] += 1
                return False

        except Exception as e:
            self.main_app.log_message(f"[TaskFlow] 分段解析处理异常: {str(e)}")
            self.stats['error_count'] += 1
            return False

    def _execute_segmented_transcription_from_action(self, task: Dict, task_index: int, flow_action: Dict) -> bool:
        """执行分段转录处理 - 从流转决策处理

        Args:
            task: 任务信息
            task_index: 任务索引
            flow_action: 包含分段转录信息的流转决策（字典）

        Returns:
            bool: 是否成功执行
        """
        try:
            if self.debug_mode:
                self.main_app.log_message("[TaskFlow] 开始执行分段转录处理")

            input_file = task.get('path', '')
            task_folder = self.main_app._get_task_project_folder(input_file)
            file_name = os.path.splitext(os.path.basename(input_file))[0]

            # 获取分段转录信息
            segments_need_transcription = flow_action.get('segments_need_transcription', [])

            if not segments_need_transcription:
                self.main_app.log_message("[TaskFlow] 没有需要转录的分段")
                # 检查是否可以进入字幕生成阶段
                return self.trigger_next_stage(CombinationStage.TRANSCRIPTION_PROCESSING, task, task_index)

            self.main_app.log_message(f"[TaskFlow] 需要转录 {len(segments_need_transcription)} 个分段")

            # 按分段组织转录任务
            segments_by_name = {}
            for item in segments_need_transcription:
                segment_name = item['segment']
                service = item['service']

                if segment_name not in segments_by_name:
                    segments_by_name[segment_name] = []
                segments_by_name[segment_name].append(service)

            # 执行分段转录
            successful_segments = 0
            total_segments = len(segments_by_name)

            for segment_name, services in segments_by_name.items():
                self.main_app.log_message(f"[TaskFlow] 处理分段 {successful_segments + 1}/{total_segments}: {segment_name}")

                # 找到分段音频文件
                segment_audio_file = None
                for file in os.listdir(task_folder):
                    if file.startswith(segment_name) and file.endswith('.mp3'):
                        segment_audio_file = os.path.join(task_folder, file)
                        break

                if not segment_audio_file:
                    self.main_app.log_message(f"[TaskFlow] 找不到分段音频文件: {segment_name}")
                    continue

                # 转录该分段的所有需要的服务
                segment_success = False
                for service in services:
                    try:
                        self.main_app.log_message(f"[TaskFlow] 开始转录分段: {segment_name} - {service}")

                        success = False
                        if service == 'ElevenLabs':
                            success = self.main_app._run_elevenlabs_transcription(
                                segment_audio_file, input_file, segment_name
                            )
                        elif service == 'AssemblyAI':
                            success = self.main_app._run_assemblyai_transcription(
                                segment_audio_file, input_file, segment_name
                            )
                        elif service == 'Deepgram':
                            success = self.main_app._run_deepgram_transcription(
                                segment_audio_file, input_file, segment_name
                            )

                        if success:
                            self.main_app.log_message(f"[TaskFlow] 分段转录成功: {segment_name} - {service}")

                            # 🔧 新增：转录成功后立即进行解析
                            try:
                                parsing_success = self.main_app._parse_service_json(
                                    task_folder, segment_name, service
                                )
                                if parsing_success:
                                    self.main_app.log_message(f"[TaskFlow] 分段解析成功: {segment_name} - {service}")
                                    segment_success = True
                                else:
                                    self.main_app.log_message(f"[TaskFlow] 分段解析失败: {segment_name} - {service}")
                            except Exception as parse_e:
                                self.main_app.log_message(f"[TaskFlow] 分段解析异常: {segment_name} - {service}: {str(parse_e)}")
                        else:
                            self.main_app.log_message(f"[TaskFlow] 分段转录失败: {segment_name} - {service}")

                    except Exception as e:
                        self.main_app.log_message(f"[TaskFlow] 分段转录异常: {segment_name} - {service}: {str(e)}")

                if segment_success:
                    successful_segments += 1

            # 检查转录结果
            if successful_segments > 0:
                self.main_app.log_message(f"[TaskFlow] 分段转录完成: {successful_segments}/{total_segments}")

                # 检查是否所有分段都完成了
                from .segmentation_utils import SegmentationUtils
                segment_files = SegmentationUtils.find_segment_files(task_folder, file_name)

                if segment_files:
                    # 重新检查分段状态
                    enabled_services = self.main_app._get_enabled_asr_services()
                    status = self._check_segmented_asr_status(task_folder, segment_files, enabled_services)

                    if status.get('action') == 'check_subtitle_readiness':
                        self.main_app.log_message("[TaskFlow] 所有分段转录完成，进入字幕生成阶段")
                        return self.trigger_next_stage(CombinationStage.TRANSCRIPTION_PROCESSING, task, task_index)
                    elif status.get('action') == 'parse_segments':
                        self.main_app.log_message("[TaskFlow] 分段转录完成，开始解析")
                        return self._execute_segmented_parsing(task, task_index, status)
                    else:
                        self.main_app.log_message(f"[TaskFlow] 部分分段ASR未完成: {status.get('reason', 'unknown')}")
                        return False
                else:
                    self.main_app.log_message("[TaskFlow] 分段转录完成，但无法检查状态")
                    return False
            else:
                self.main_app.log_message("[TaskFlow] 所有分段转录都失败")
                self.stats['error_count'] += 1
                return False

        except Exception as e:
            self.main_app.log_message(f"[TaskFlow] 分段转录处理异常: {str(e)}")
            self.stats['error_count'] += 1
            return False

    def _execute_audio_processing(self, task: Dict, task_index: int) -> bool:
        """执行音频处理

        Args:
            task: 任务信息
            task_index: 任务索引

        Returns:
            bool: 是否成功执行
        """
        try:
            if self.debug_mode:
                self.main_app.log_message("[TaskFlow] 开始执行音频处理")

            input_file = task.get('path', '')
            task_name = task.get('name', 'unknown')

            # 检查音频处理器可用性
            if not self.main_app.audio_processor:
                self.main_app.log_message("[TaskFlow] 音频处理器不可用")
                self.stats['error_count'] += 1
                return False

            # 获取任务文件夹
            task_folder = self.main_app._get_task_project_folder(input_file)
            if not task_folder:
                self.main_app.log_message("[TaskFlow] 无法获取任务文件夹")
                self.stats['error_count'] += 1
                return False

            # 生成输出路径
            file_name = os.path.splitext(os.path.basename(input_file))[0]
            output_file = os.path.join(task_folder, f"{file_name}.wav")

            self.main_app.log_message(f"[TaskFlow] 开始音频处理: {input_file} -> {output_file}")

            # 执行音频处理
            try:
                result = self.main_app.audio_processor.process_file(
                    input_file,
                    output_file,
                    progress_callback=None  # 由EISA管理进度
                )

                if not result or not result.success:
                    error_msg = result.error_message if result else "未知错误"
                    self.main_app.log_message(f"[TaskFlow] 音频处理失败: {error_msg}")
                    self.stats['error_count'] += 1
                    return False

                # 验证输出文件
                actual_output = result.output_path if result.output_path else output_file
                if not os.path.exists(actual_output):
                    self.main_app.log_message(f"[TaskFlow] 输出文件不存在: {actual_output}")
                    self.stats['error_count'] += 1
                    return False

                self.main_app.log_message(f"[TaskFlow] 音频处理成功: {actual_output}")

                # 检查是否需要音频分割
                if self._should_segment_audio(actual_output):
                    # 需要分割，在音频处理阶段完成
                    self.main_app.log_message(f"[TaskFlow] 音频时长超过阈值，开始音频分割")
                    segmentation_result = self._execute_audio_segmentation(actual_output)

                    if segmentation_result.success:
                        self.main_app.log_message(f"[TaskFlow] 音频分割完成，生成 {len(segmentation_result.segment_files)} 个分段")
                        # 分割完成，直接进入分段转录
                        return self._execute_segmented_transcription_from_result(task, task_index, segmentation_result)
                    else:
                        # 分割失败，fallback到正常转录
                        self.main_app.log_message(f"[TaskFlow] 音频分割失败，fallback到正常转录: {segmentation_result.error_message}")
                        return self.trigger_next_stage(CombinationStage.AUDIO_PROCESSING, task, task_index)
                else:
                    # 不需要分割，正常进入转录阶段
                    return self.trigger_next_stage(CombinationStage.AUDIO_PROCESSING, task, task_index)

            except InterruptedError:
                self.main_app.log_message("[TaskFlow] 音频处理被取消")
                return False
            except Exception as e:
                self.main_app.log_message(f"[TaskFlow] 音频处理异常: {str(e)}")
                self.stats['error_count'] += 1
                return False

        except Exception as e:
            self.main_app.log_message(f"[TaskFlow] 音频处理执行异常: {str(e)}")
            self.stats['error_count'] += 1
            return False

    def get_performance_stats(self) -> Dict:
        """获取性能统计信息

        Returns:
            Dict: 包含性能统计的字典
        """
        total_processed = self.stats['total_tasks_processed']
        if total_processed == 0:
            return {
                'total_processed': 0,
                'success_rate': 0.0,
                'error_rate': 0.0,
                'efficiency_score': 0.0
            }

        success_rate = self.stats['success_count'] / total_processed
        error_rate = self.stats['error_count'] / total_processed

        # 效率评分：基于成功率和错误率
        efficiency_score = success_rate * (1 - error_rate)

        return {
            'total_processed': total_processed,
            'success_count': self.stats['success_count'],
            'error_count': self.stats['error_count'],
            'success_rate': round(success_rate * 100, 2),
            'error_rate': round(error_rate * 100, 2),
            'efficiency_score': round(efficiency_score * 100, 2)
        }

    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_tasks_processed': 0,
            'success_count': 0,
            'error_count': 0
        }
        if self.debug_mode:
            self.main_app.log_message("[TaskFlow] 统计信息已重置")

    def optimize_performance(self):
        """性能优化建议

        Returns:
            List[str]: 优化建议列表
        """
        suggestions = []
        stats = self.get_performance_stats()

        if stats['total_processed'] == 0:
            return ["暂无性能数据"]

        if stats['error_rate'] > 10:
            suggestions.append(f"错误率较高({stats['error_rate']}%)，建议检查API配置和网络连接")

        if stats['success_rate'] < 70:
            suggestions.append(f"成功率较低({stats['success_rate']}%)，建议启用调试模式排查问题")

        if stats['efficiency_score'] > 90:
            suggestions.append("性能表现优秀！")
        elif stats['efficiency_score'] > 70:
            suggestions.append("性能表现良好")
        else:
            suggestions.append("建议检查配置以提升性能")

        return suggestions if suggestions else ["性能表现正常"]




# === 旧架构TaskStage已完全移除，现已统一使用EISA架构 ===


class EvaTransMainApp:
    """EvaTrans主应用类 - 音视频转录和字幕生成的核心应用

    核心功能：
    - 音视频文件处理和转换
    - 多ASR服务转录支持
    - 多LLM API字幕生成
    - 分段处理和合并
    - 任务进度管理和状态跟踪
    """
    def __init__(self, page: ft.Page):
        self.page = page

        # 配置管理器
        self.config_manager = ConfigManager(self)

        # 日志系统
        self.log_system = LogSystem(self)

        # 主题管理器
        self.theme_manager = ThemeManager(self)

        # 设置管理器
        self.settings_manager = SettingsManager(self)

        # EISA计算引擎
        self.progress_calculator = CombinationProgressCalculator(self)

        # 任务流转控制器（EISA架构扩展）
        self.task_flow_controller = TaskFlowController(self)

        # 状态管理
        self.current_main_tab = 0
        self._programmatic_tab_change = False

        # 任务取消机制（简化）
        self.conversion_thread = None

        # 任务列表数据
        self.task_list = []
        self.task_list_view = None
        self.conversion_cancelled = False

        # 音频处理服务（一体化）
        self.audio_processor = None

        try:
            # FFmpeg检测已移除，直接初始化音频系统


            audio_services_path = os.path.join(os.path.dirname(__file__), '..', 'services', 'audio')
            if audio_services_path not in sys.path:
                sys.path.insert(0, os.path.dirname(os.path.join(os.path.dirname(__file__), '..')))

            from services.audio import AudioProcessor

            # 一体化音频处理器（集成了分析、转换功能）
            self.audio_processor = AudioProcessor()

        except Exception as e:
            self.audio_processor = None
            self.log_system._pending_log_messages = [f"[WARN] 音频处理系统初始化失败: {e}"]


        # UI 组件
        self.log_area = None
        self.start_button = None

        # 设置苹果简约风格的字体和主题
        self._setup_apple_theme()

        self._create_ui()
        self._setup_cleanup_handlers()
        self._setup_window_handlers()

    # 配置访问器属性 - 保持向后兼容

    @property

    def config(self):
        return self.config_manager.config


    @property

    def llm_settings(self):
        return self.config_manager.llm_settings

    @property

    def elevenlabs_settings(self):
        return self.config_manager.elevenlabs_settings

    @property

    def assemblyai_settings(self):
        return self.config_manager.assemblyai_settings

    @property

    def deepgram_settings(self):
        return self.config_manager.deepgram_settings

    @property

    def asr_service_switches(self):
        return self.config_manager.asr_service_switches

    # 日志系统访问器 - 保持向后兼容

    def log_message(self, message: str, is_debug: bool = False):
        """日志消息接口"""
        self.log_system.log_message(message, is_debug)

    def log_test_message(self, config_id: str, message: str, clear_first: bool = False):
        """测试日志消息接口"""
        self.log_system.log_test_message(config_id, message, clear_first)

    def clear_test_log(self, config_id: str):
        """清空测试日志接口"""
        self.log_system.clear_test_log(config_id)

    # 日志系统属性访问器 - 保持向后兼容

    @property

    def test_log_areas(self):
        """测试日志区域访问器"""
        return self.log_system.test_log_areas

    @property

    def test_log_cleared(self):
        """测试日志清空状态访问器"""
        return self.log_system.test_log_cleared

    @property

    def log_messages_buffer(self):
        """日志消息缓冲区访问器"""
        return self.log_system.log_messages_buffer

    @property

    def global_ui_handler(self):
        """全局UI日志处理器访问器"""
        return self.log_system.global_ui_handler


    # 设置管理器属性访问器 - 保持向后兼容

    @property

    def current_settings_view(self):
        """当前设置视图访问器"""
        return self.settings_manager.current_settings_view

    @current_settings_view.setter

    def current_settings_view(self, value):
        """当前设置视图设置器"""
        self.settings_manager.current_settings_view = value

    @property

    def menu_items(self):
        """菜单项访问器"""
        return self.settings_manager.menu_items

    @property

    def settings_content(self):
        """设置内容访问器"""
        return self.settings_manager.settings_content

    # FFmpeg检测机制已移除

    def _create_safe_completion_callback(self, task_index: int):
        """创建安全的完成回调函数，支持取消检查"""

        def safe_callback(success: bool, error_message: str = None):
            try:
                # 检查取消状态
                if self.conversion_cancelled:
                    return

                # 处理简化的完成回调（使用新的统一方法）
                if success:
                    # 音频处理完成，使用统一进度计算器自动同步状态
                    self._update_task_progress(task_index)
                else:
                    error_msg = error_message or '音频处理失败'
                    # 详细错误信息记录到日志，状态使用静态字符串
                    self.log_message(f"[ERROR] 音频处理失败详情: {error_msg}")
                    self._update_task_progress(task_index, custom_status='音频处理失败')

            except Exception as e:
                # 进度回调失败不应该影响主处理流程
                pass

        return safe_callback

    def _process_pending_log_messages(self):
        """处理延迟的日志消息"""
        self.log_system.process_pending_log_messages()

    def _setup_cleanup_handlers(self):
        """设置应用清理处理器"""
        try:
            import atexit
            atexit.register(self._cleanup_on_exit)
        except Exception:
            pass

    def _setup_window_handlers(self):
        """设置窗口事件处理器"""
        try:
            if hasattr(self.page, 'on_window_event'):
                self.page.on_window_event = self._on_window_event
        except Exception:
            pass

    def _on_window_event(self, e):
        """窗口事件处理器"""
        try:
            if e.data == "close":
                self._cleanup_on_exit()
        except Exception:
            pass

    def _cleanup_on_exit(self):
        """简化的退出清理"""
        try:
            # 设置取消标志
            self.conversion_cancelled = True

            # ASR服务无需特殊清理

        except Exception:
            pass


    def _create_ui(self):
        """创建用户界面"""
        # 设置页面图标
        try:
            self.page.window_icon = "assets/icon.png"
        except:
            pass

        # 创建顶部按钮
        self.open_directory_button = ft.IconButton(
            icon=ft.Icons.FOLDER_OPEN,
            tooltip="打开项目目录",
            on_click=self._open_project_directory,

        )

        self.about_button = ft.IconButton(
            icon=ft.Icons.INFO_OUTLINE,
            tooltip="关于",
            on_click=self._show_about,

        )

        # 使用固定苹果简约风格

        # 使用Row将标签页和按钮放在同一行
        colors = self.theme_manager.get_theme_colors()

        # 创建标签页
        tabs = ft.Tabs(
            selected_index=0,
            animation_duration=300,
            on_change=self._on_tab_change,
            indicator_border_radius=4,  # 圆角指示器
            divider_color="transparent",  # 透明分割线
            scrollable=False,  # 禁用滚动
            # 字体继承页面级设置
            label_text_style=ft.TextStyle(
                size=20,  # 字体大小
                weight=ft.FontWeight.W_700,  # 更粗的字体
                letter_spacing=0.8  # 增加字母间距
            ),
            unselected_label_text_style=ft.TextStyle(
                size=18,  # 未选中时也增大
                weight=ft.FontWeight.W_500,  # 中等粗细
                letter_spacing=0.5
            ),
            tabs=[
                ft.Tab(
                    text="工作台",
                    content=self._create_main_tab()
                ),
                ft.Tab(
                    text="配置中心",
                    content=self.settings_manager.create_settings_tab()
                )
            ],
            expand=True
        )

        # 保存标签页实例
        self.tabs = tabs

        # 创建右侧按钮列（垂直排布）- 移除主题切换按钮
        right_buttons = ft.Column([
            self.open_directory_button,
            self.about_button
        ], tight=True, spacing=5)

        # 使用Row布局，标签页占据大部分空间，右边是垂直排布的按钮
        top_bar = ft.Row([
            ft.Container(content=tabs, expand=True),  # 标签页容器
            ft.Container(content=right_buttons, width=50)  # 右侧按钮列
        ],
        expand=True,
        vertical_alignment=ft.CrossAxisAlignment.START  # 垂直对齐到顶部
        )

        # 苹果简约风格：纯白背景
        self.page.bgcolor = "#FFFFFF"  # 纯白背景
        self.page.add(top_bar)
        self.page.update()

        # 处理延迟的日志消息
        self._process_pending_log_messages()

    def _on_tab_change(self, e):
        """处理主标签页切换事件"""
        if e.control and hasattr(e.control, 'selected_index'):
            self.current_main_tab = e.control.selected_index

            # 标签页切换处理（已移除日志输出）
            pass

    def _show_about(self, e):
        """显示关于对话框 - 使用现代化卡片设计"""
        # 创建带关闭按钮的卡片
        def close_about(e):
            self.page.close(about_dialog)

        # 创建关于卡片 - 内联实现
        about_card_content = ft.Container(
            width=400,
            height=210,
            bgcolor=ft.Colors.with_opacity(0.9, "#F0F8FF"),  # 浅蓝背景
            border_radius=16,
            border=ft.border.all(1, ft.Colors.with_opacity(0.3, "#5B9BD5")),
            shadow=ft.BoxShadow(
                spread_radius=2,
                blur_radius=20,
                color=ft.Colors.with_opacity(0.2, ft.Colors.BLACK),
                offset=ft.Offset(0, 8)
            ),
            content=ft.Container(
                padding=ft.padding.all(35),
                content=ft.Row([
                    ft.Container(
                        content=ft.Column([
                            ft.Text(
                                "EvaTrans",
                                size=36,
                                weight=ft.FontWeight.W_800,
                                color="#1D1D1F",
                                font_family="Microsoft YaHei UI"
                            ),
                            ft.Container(height=35),  # 增加更多间距，让by evafans下移到v1.0底部水平线下方
                            ft.Text(
                                "by evafans",
                                size=16,
                                weight=ft.FontWeight.W_400,
                                color="#5B9BD5",
                                font_family="Microsoft YaHei UI"
                            )
                        ],
                        spacing=12,
                        alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                        ),
                        expand=True
                    ),
                    ft.Container(
                        content=ft.Container(
                            content=ft.Text(
                                "v1.0",
                                size=28,
                                weight=ft.FontWeight.W_700,
                                color="#FFFFFF",
                                font_family="Microsoft YaHei UI"
                            ),
                            padding=ft.padding.symmetric(horizontal=22, vertical=14),
                            bgcolor="#5B9BD5",
                            border_radius=25,
                            shadow=ft.BoxShadow(
                                spread_radius=0,
                                blur_radius=10,
                                color=ft.Colors.with_opacity(0.3, "#5B9BD5"),
                                offset=ft.Offset(0, 4)
                            )
                        ),
                        alignment=ft.alignment.top_center  # 调整为顶部居中对齐
                    )
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
            )
        )

        # 创建关于卡片内容，在右上角添加x关闭按钮
        card_with_close = ft.Stack([
            # 主卡片内容
            about_card_content,
            # 右上角关闭按钮
            ft.Container(
                content=ft.IconButton(
                    icon=ft.Icons.CLOSE,
                    icon_size=16,
                    icon_color=ft.Colors.with_opacity(0.6, "#86868B"),  # 淡淡的灰色
                    on_click=close_about,
                    style=ft.ButtonStyle(
                        shape=ft.CircleBorder(),
                        bgcolor=ft.Colors.with_opacity(0.1, "#FFFFFF"),  # 淡淡的白色背景
                        overlay_color=ft.Colors.with_opacity(0.1, "#86868B")  # 悬停效果
                    )
                ),
                right=8,
                top=8,
                width=28,
                height=28
            )
        ])

        # 创建模态对话框
        about_dialog = ft.AlertDialog(
            modal=True,
            content=card_with_close,
            bgcolor=ft.Colors.TRANSPARENT,  # 透明背景，只显示卡片
            content_padding=0  # 移除内边距
        )
        self.page.open(about_dialog)

    def _setup_apple_theme(self):
        """设置苹果简约风格主题"""
        # 使用博客文章中的正确方法设置全局字体
        # Microsoft YaHei UI - UI优化版本，完美支持中英文
        self.page.theme = ft.Theme(font_family="Microsoft YaHei UI")

        # 设置页面基础样式
        self.page.bgcolor = "#FFFFFF"  # 苹果简约纯白背景
        self.page.padding = 0  # 移除默认内边距

    def _create_main_tab(self):
        """创建主运行标签页"""
        colors = self.theme_manager.get_theme_colors()

        # 创建任务列表区域
        task_list_section = self._create_task_list_section()

        # 使用日志系统创建日志区域
        self.log_area = self.log_system.create_log_area(colors)
        self.log_system.log_area = self.log_area  # 设置引用

        # 使用日志系统创建DEBUG开关
        self.debug_log_switch = self.log_system.create_debug_switch(self._on_debug_log_switch_change)

        # 日志标题行（包含DEBUG开关）
        log_header = ft.Row([
            ft.Text("运行日志", size=18, weight="bold"),  # 使用原生主题颜色
            ft.Container(expand=True),  # 占位符，推动开关到右边
            ft.Row([
                ft.Icon("bug_report", size=16),  # 使用原生主题颜色
                ft.Text("DEBUG", size=12),  # 使用原生主题颜色
                self.debug_log_switch
            ], spacing=5)
        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)

        # 日志区域容器 - 苹果简约风格
        log_section = ft.Container(
            content=ft.Column([
                log_header,  # 使用新的标题行
                ft.Container(height=15),
                self.log_area
            ]),
            padding=30,
            border_radius=12,
            margin=20,
            expand=True,
            bgcolor=colors['glass_bg'],  # 微灰背景
            border=ft.border.all(1, colors['border_color']),  # 极浅灰边框
            # 移除阴影和毛玻璃效果，保持苹果简约风格
        )

        # 使用Column布局：上下排布，任务列表和日志两个区域自动分配高度
        return ft.Container(
            content=ft.Column([
                ft.Container(
                    content=task_list_section,  # 上方任务列表
                    expand=1  # 自动分配，占1份
                ),
                ft.Container(
                    content=log_section,        # 下方运行日志
                    expand=1  # 自动分配，占1份，与任务列表等高
                )
            ], expand=True),
            padding=20
        )

    def _create_task_list_section(self):
        """创建任务列表区域"""
        colors = self.theme_manager.get_theme_colors()

        # 任务列表标题行
        header_row = ft.Container(
            content=ft.Row([
                ft.Container(
                    content=ft.Text("文件名", size=14, weight="bold", color=colors['text_color']),
                    expand=2,
                    alignment=ft.alignment.center
                ),
                ft.Container(
                    content=ft.Text("大小", size=14, weight="bold", color=colors['text_color']),
                    expand=1,
                    alignment=ft.alignment.center
                ),
                ft.Container(
                    content=ft.Text("类型", size=14, weight="bold", color=colors['text_color']),
                    expand=1,
                    alignment=ft.alignment.center
                ),
                ft.Container(
                    content=ft.Text("阶段", size=14, weight="bold", color=colors['text_color']),
                    expand=1,
                    alignment=ft.alignment.center
                ),
                ft.Container(
                    content=ft.Text("状态", size=14, weight="bold", color=colors['text_color']),
                    expand=1,
                    alignment=ft.alignment.center
                ),
                ft.Container(
                    content=ft.Text("进度", size=14, weight="bold", color=colors['text_color']),
                    expand=1,
                    alignment=ft.alignment.center
                ),
            ]),
            padding=ft.padding.symmetric(horizontal=10, vertical=10),
            bgcolor=colors['menu_selected'],
            border_radius=ft.border_radius.only(top_left=8, top_right=8)
        )

        # 任务列表视图
        self.task_list_view = ft.Column(
            controls=[],
            spacing=2,
            scroll=ft.ScrollMode.AUTO
        )

        # 任务列表容器
        task_list_container = ft.Container(
            content=ft.Column([
                header_row,
                ft.Container(
                    content=self.task_list_view,
                    expand=True,   # 自动分配高度
                    height=120,    # 设置最小高度，确保空列表时有合适的显示区域
                    bgcolor=colors['bg_color'],  # 使用实心背景
                    border_radius=ft.border_radius.only(bottom_left=8, bottom_right=8),
                    padding=5
                )
            ]),
            border=ft.border.all(1, colors['border_color']),
            border_radius=8
        )

        # 操作按钮 - 苹果简约风格
        add_file_btn = ft.ElevatedButton(
            "添加文件",
            icon=ft.Icons.ADD_CIRCLE_OUTLINE,
            bgcolor=colors['accent_color'],  # 雾霾蓝
            color="#FFFFFF",  # 白色文字
            on_click=self._add_files,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=8),
                elevation=0,
            )
        )

        add_folder_btn = ft.ElevatedButton(
            "添加文件夹",
            icon=ft.Icons.FOLDER_OPEN,
            bgcolor=colors['accent_color'],  # 雾霾蓝
            color="#FFFFFF",  # 白色文字
            on_click=self._add_folder,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=8),
                elevation=0,
            )
        )

        clear_list_btn = ft.ElevatedButton(
            "清空列表",
            icon=ft.Icons.CLEAR_ALL,
            bgcolor=colors['glass_bg'],  # 微灰背景
            color=colors['secondary_text_color'],  # 苹果灰文字
            on_click=self._clear_task_list,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=8),
                elevation=0,
                side=ft.BorderSide(1, colors['border_color']),  # 浅灰边框
            )
        )

        # 开始转换按钮 - 苹果简约风格
        self.start_button = ft.ElevatedButton(
            "开始",
            icon=ft.Icons.PLAY_ARROW,
            bgcolor=colors['accent_color'],  # 雾霾蓝
            color="#FFFFFF",  # 白色文字
            on_click=self._start_conversion,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=8),
                elevation=0,
            )
        )

        # 取消按钮 - 苹果简约风格
        self.cancel_button = ft.ElevatedButton(
            "取消",
            icon=ft.Icons.STOP,
            bgcolor=colors['glass_bg'],  # 微灰背景
            color=colors['secondary_text_color'],  # 苹果灰文字
            on_click=self._cancel_conversion,
            disabled=True,  # 初始状态禁用
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=8),
                elevation=0,
                side=ft.BorderSide(1, colors['border_color']),  # 浅灰边框
            )
        )

        # 左侧按钮行（文件操作）
        left_button_row = ft.Row([
            add_file_btn,
            add_folder_btn,
            clear_list_btn
        ], spacing=10)

        # 右侧按钮行（转换控制）
        right_button_row = ft.Row([
            self.start_button,
            self.cancel_button
        ], spacing=10)

        # 底部按钮区域
        bottom_buttons = ft.Row([
            left_button_row,
            right_button_row
        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)

        # 整个任务列表区域 - 苹果简约风格
        return ft.Container(
            content=ft.Column([
                ft.Text("任务列表", size=18, weight="bold", color=colors['text_color']),
                ft.Container(height=8),
                task_list_container,
                ft.Container(height=8),
                bottom_buttons  # 底部按钮区域
            ]),
            padding=30,
            bgcolor=colors['glass_bg'],  # 微灰背景
            border_radius=12,
            margin=20,
            border=ft.border.all(1, colors['border_color']),  # 极浅灰边框
            # 移除阴影和毛玻璃效果，保持苹果简约风格
        )

    def _on_llm_switch_change(self, index: int, enabled: bool):
        """LLM开关变更处理"""
        try:
            service = get_multi_llm_service()
            if len(service.configs) > index:
                config = service.configs[index]
                service.update_config_field(config.id, 'enabled', enabled)
                self.log_message(f"已{'启用' if enabled else '禁用'}API{index+1}")
        except Exception as ex:
            self.log_message(f"更新LLM开关状态时出错: {ex}")


    def _update_config_enabled(self, config_id: str, enabled: bool):
        """更新配置启用状态"""
        try:
            service = get_multi_llm_service()
            service.update_config_field(config_id, 'enabled', enabled)
            self.log_message(f"已{'启用' if enabled else '禁用'}配置: {config_id}")
            # LLM设置视图刷新由settings_manager处理
        except Exception as ex:
            self.log_message(f"更新配置启用状态时出错: {ex}")

    def _update_config_temperature(self, config_id: str, temperature: float):
        """更新配置温度参数"""
        try:
            service = get_multi_llm_service()
            service.update_config_field(config_id, 'temperature', temperature)
            # 不记录日志，避免日志过多
        except Exception as ex:
            self.log_message(f"更新配置温度参数时出错: {ex}")


    def _update_config_field(self, config_id: str, field_name: str, value):
        """更新配置字段"""
        try:
            service = get_multi_llm_service()
            service.update_config_field(config_id, field_name, value)
            # 不记录每个字段变更的日志，避免日志过多
        except Exception as ex:
            self.log_message(f"更新配置字段时出错: {ex}")

    def _on_debug_log_switch_change(self, e):
        """DEBUG日志显示开关变更处理"""
        try:
            show_debug = e.control.value
            self.config['show_debug_logs'] = show_debug
            self.config_manager.save_config()

            status = "显示" if show_debug else "隐藏"
            self.log_message(f"DEBUG日志已设置为{status}")

            # 实时刷新日志显示
            self.log_system.refresh_log_display()

            self.page.update()
        except Exception as ex:
            self.log_message(f"更新DEBUG日志显示设置时出错: {ex}")

    def _start_transcription_after_conversion(self, original_file_path: str, converted_audio_path: str, task_name: str, task_index: int = None):
        """音频转换完成后开始智能增量转录（纯TaskFlowController版本）

        这个方法已经被完全重构，所有逻辑都通过TaskFlowController统一处理，
        确保EISA架构的纯洁性和一致性。
        """
        try:
            # 让EISA重新计算进度（基于明确阶段和文件状态）
            if task_index is not None:
                self._update_task_progress(task_index)

            self.log_message(f"开始智能增量转录: {task_name}")

            # === 统一逻辑：完全委托给TaskFlowController ===
            if hasattr(self, 'task_flow_controller') and self.task_flow_controller:
                self.log_message("[TaskFlow] 使用TaskFlowController处理转录逻辑")

                # 构造任务对象
                task = {
                    'path': original_file_path,
                    'name': task_name
                }

                # 获取文件信息
                task_folder = os.path.dirname(converted_audio_path)
                file_name = os.path.splitext(os.path.basename(original_file_path))[0]

                # 检测当前阶段
                current_stage = self.task_flow_controller.detect_current_stage(task_folder, file_name)

                # 处理增量逻辑
                flow_action = self.task_flow_controller.handle_incremental_processing(task_folder, file_name, current_stage)

                # 执行相应的操作
                if flow_action.get('action') == 'generate_subtitles':
                    # 所有ASR服务已完成，直接进入字幕生成
                    self.log_message("[TaskFlow] ASR服务已完成，开始字幕生成")
                    success = self._generate_subtitles_sync(task, task_index)
                    if success:
                        self.log_message("[TaskFlow] 字幕生成完成")
                    else:
                        self.log_message("[TaskFlow] 字幕生成失败")
                    return

                elif flow_action.get('action') == 'parse_and_continue':
                    # 需要解析，然后可能进入字幕生成
                    self.log_message("[TaskFlow] 执行解析并继续处理")
                    success = self.task_flow_controller._execute_parse_and_continue(task, task_index, flow_action)
                    if success:
                        self.log_message("[TaskFlow] 解析并继续处理完成")
                    else:
                        self.log_message("[TaskFlow] 解析并继续处理失败")
                    return

                elif flow_action.get('action') == 'transcribe':
                    # 需要转录，使用TaskFlowController的转录逻辑
                    self.log_message("[TaskFlow] 需要转录，使用TaskFlowController")
                    success = self.task_flow_controller._execute_transcription(task, task_index, flow_action)
                    if success:
                        self.log_message("[TaskFlow] 转录处理完成")
                    else:
                        self.log_message("[TaskFlow] 转录处理失败")
                    return

                elif flow_action.get('action') == 'error':
                    # 处理错误
                    error_reason = flow_action.get('reason', 'unknown')
                    self.log_message(f"[TaskFlow] 处理错误: {error_reason}")
                    if task_index is not None:
                        self._update_task_progress(task_index, custom_status='配置错误')
                    return

                else:
                    # 未知操作
                    self.log_message(f"[TaskFlow] 未知操作: {flow_action.get('action')}")
                    if task_index is not None:
                        self._update_task_progress(task_index, custom_status='处理失败')
                    return
            else:
                # TaskFlowController不可用
                self.log_message("[ERROR] TaskFlowController不可用")
                if task_index is not None:
                    self._update_task_progress(task_index, custom_status='系统错误')

        except Exception as e:
            self.log_message(f"转录过程出错: {task_name} - {str(e)}")
            # 转录过程异常，更新任务状态为失败
            if task_index is not None:
                self._update_task_progress(task_index, custom_status='音频转录异常')

    # _start_transcription_after_conversion_original方法已删除
    # 原有的复杂fallback逻辑已被移除，所有转录逻辑现在统一由TaskFlowController处理

    def _is_any_task_in_progress(self) -> bool:
        """EISA智能进度检测：完全基于EISA状态融合的任务进度检测"""
        try:
            # EISA智能检测：使用状态融合检测活跃处理状态
            for task in self.task_list:
                progress = self.progress_calculator.calculate_progress(task)

                # 检查是否有活跃处理状态（EISA核心特性）
                if progress.stage.is_active:
                    return True

            return False

        except Exception as e:
            self.log_message(f"EISA进度检测异常: {e}")
            return False

    def _compute_button_state(self):
        """EISA智能按钮控制：完全基于EISA状态融合的按钮状态计算"""
        try:
            # EISA智能状态检测：检查是否有活跃处理状态的任务
            for task in self.task_list:
                progress = self.progress_calculator.calculate_progress(task)

                # 检查是否有活跃处理状态（EISA核心特性）
                if progress.stage.is_active:
                    return ("进行中...", True)

            # EISA智能状态检测：检查是否有未完成的任务需要继续
            has_incomplete_tasks = False
            for task in self.task_list:
                progress = self.progress_calculator.calculate_progress(task)
                if progress.stage != CombinationStage.COMPLETED:
                    has_incomplete_tasks = True
                    break

            if has_incomplete_tasks:
                return ("继续", False)

            # 所有任务完成或没有任务
            return ("开始", False)

        except Exception as e:
            self.log_message(f"EISA按钮状态计算异常: {e}")
            return ("开始", False)

    def update_button_state(self, immediate=False):
        """更新按钮状态"""
        try:
            if immediate:
                # 立即更新
                self._apply_button_state()
            else:
                # 调度更新
                self._apply_button_state()
        except Exception as e:
            self.log_message(f"响应式按钮更新异常: {e}")

    def _apply_button_state(self):
        """应用按钮状态（线程安全版本）"""
        try:
            new_text, new_disabled = self._compute_button_state()

            # 只在状态真正改变时更新UI
            if (self.start_button.text != new_text or
                self.start_button.disabled != new_disabled):

                old_text = self.start_button.text
                old_disabled = self.start_button.disabled

                # 线程安全的UI更新
                def update_button_ui():
                    self.start_button.text = new_text
                    self.start_button.disabled = new_disabled

                    # 修复：同时更新取消按钮状态
                    self._update_cancel_button_state()

                    self.page.update()
                    self.log_message(f"[BUTTON] 按钮状态更新: '{old_text}' -> '{new_text}' (禁用: {old_disabled} -> {new_disabled})")

                # 检查当前线程并选择合适的更新方式
                try:
                    if threading.current_thread() == threading.main_thread():
                        update_button_ui()
                    else:
                        self.page.run_thread(update_button_ui)
                except Exception:
                    # 如果线程检查失败，总是使用page.run_thread（更安全）
                    self.page.run_thread(update_button_ui)

        except Exception as e:
            self.log_message(f"应用按钮状态异常: {e}")

    def _parse_task_stage_and_status(self, status: str) -> tuple:
        """EISA状态解析：基于智能枚举的精确状态匹配"""
        # 直接从CombinationStage枚举中查找匹配
        for stage in CombinationStage:
            if stage.display_text in status:
                return stage.display_text, stage.icon

        # 处理自定义状态（包含冒号的详细状态）
        base_status = status.split(':')[0].strip()
        for stage in CombinationStage:
            if stage.display_text == base_status:
                return base_status, stage.icon

        # 处理音频处理器的特殊状态
        audio_status_map = {
            "开始处理": ("音频处理中", "🔄"),
            "处理完成": ("音频处理中", "🔄"),  # 临时状态，很快会被同步
        }

        if status in audio_status_map:
            return audio_status_map[status]

        # 处理失败状态
        if '失败' in status:
            return "处理失败", "❌"

        # 处理错误状态
        if '错误' in status or 'ERROR' in status.upper():
            return "处理错误", "❌"

        # 默认返回处理中状态
        return "处理中", "🔄"


    def _process_transcription_results(self, task_folder: str, base_filename: str, completed_services: list) -> list:
        """
        处理转录结果，解析并生成parsed.json文件

        Args:
            task_folder: 任务文件夹路径
            base_filename: 基础文件名
            completed_services: 已完成转录的服务列表

        Returns:
            list: 成功解析并生成parsed.json的服务列表
        """
        successfully_parsed_services = []

        try:
            from services.asr import TranscriptionParser

            self.log_message(f"开始处理转录结果: {base_filename}")

            # 创建转录解析器
            parser = TranscriptionParser()

            # 处理每个已完成的服务
            for service in completed_services:
                try:
                    # 构建JSON文件路径
                    json_filename = f"{base_filename}-{service}.json"
                    json_file_path = os.path.join(task_folder, json_filename)

                    # 检查JSON文件是否存在
                    if not os.path.exists(json_file_path):
                        self.log_message(f"[ERROR] {service} JSON文件不存在: {json_filename}")
                        continue

                    # 解析转录文件
                    self.log_message(f"解析 {service} 转录文件...")

                    # 解析文件（使用全局日志处理器）
                    result = parser.parse_file(json_file_path, source_format='auto')

                    if result and result.words:
                        # 调试：检查词汇分离功能是否生效
                        mixed_words = []
                        for word in result.words:
                            word_text = word.word
                            # 检查是否包含混合词汇的特征
                            if any(char in word_text for char in '.!?,:;()[]{}\"«»@#%&*~+=<>$€¥£。！？，、；：""''「」『』（）【】〔〕〈〉《》……—·'):
                                if "'" not in word_text and "-" not in word_text:  # 排除缩写和复合词
                                    mixed_words.append(word_text)

                        if mixed_words:
                            self.log_message(f"[DEBUG] 发现未分离的混合词汇: {mixed_words}")
                        else:
                            self.log_message(f"[DEBUG] 词汇分离功能正常，未发现混合词汇")

                        # 显示前几个词汇作为样本
                        sample_words = [w.word for w in result.words[:10]]
                        self.log_message(f"[DEBUG] 前10个词汇样本: {sample_words}")
                        # 解析成功，生成parsed.json文件
                        parsed_data = {
                            'service': service.lower(),
                            'full_text': result.full_text,
                            'words': [
                                {
                                    'word': word.word,
                                    'start_time': word.start_time,
                                    'end_time': word.end_time,
                                    'confidence': word.confidence,
                                    'speaker_id': word.speaker_id
                                }
                                for word in result.words
                            ],
                            'language': result.language,
                            'confidence': result.confidence,
                            'speaker_count': result.speaker_count,
                            'word_count': len(result.words),
                            'parsed_at': time.time()
                        }

                        # 保存parsed.json文件
                        parsed_filename = f"{base_filename}-{service}-parsed.json"
                        parsed_file_path = os.path.join(task_folder, parsed_filename)

                        with open(parsed_file_path, 'w', encoding='utf-8') as f:
                            json.dump(parsed_data, f, ensure_ascii=False, indent=2)

                        # 显示解析结果摘要
                        word_count = len(result.words)

                        self.log_message(f"[OK] {service} 解析完成: {parsed_filename}")
                        self.log_message(f"  词汇数量: {word_count}")
                        self.log_message(f"  语言: {result.language or '未知'}")
                        self.log_message(f"  置信度: {result.confidence:.2f}")

                        successfully_parsed_services.append(service)

                    else:
                        self.log_message(f"[ERROR] {service} 解析失败: 无有效转录内容")

                except Exception as e:
                    self.log_message(f"[ERROR] {service} 解析异常: {str(e)}")
                    continue

            # 汇总解析结果
            if successfully_parsed_services:
                self.log_message(f"[SUCCESS] 成功解析 {len(successfully_parsed_services)}/{len(completed_services)} 个服务")

                # 字幕生成是转录后的自然流程
                self.log_message("开始生成字幕...")

                # 获取任务索引
                task_index = -1
                for i, task in enumerate(self.task_list):
                    if os.path.basename(task_folder) in task.get('path', ''):
                        task_index = i
                        break

                # 执行字幕生成（同步）
                if task_index >= 0:
                    # 从任务列表中获取任务对象
                    task = self.task_list[task_index]
                    success = self._generate_subtitles_sync(task, task_index)
                    if success:
                        self.log_message("字幕生成完成")
                    else:
                        self.log_message("字幕生成失败")
                else:
                    # 从task_folder创建task对象，执行字幕生成
                    task = self._create_task_from_folder(task_folder, base_filename)
                    success = self._generate_subtitles_sync(task, -1)
                    if success:
                        self.log_message("字幕生成完成")
                    else:
                        self.log_message("字幕生成失败")
            else:
                self.log_message(f"[ERROR] 所有服务解析都失败: {base_filename}")

        except Exception as e:
            self.log_message(f"处理转录结果失败: {base_filename} - {str(e)}")

        return successfully_parsed_services

    def _get_completed_projects(self) -> list:
        """扫描项目目录，获取所有已完成转录的项目

        检测逻辑：
        - 原始JSON文件（elevenlabs、deepgram、assemblyai）
        - parsed.json文件（解析后的转录数据）
        - 统计转录文件数量和类型

        Returns:
            list: 已完成转录的项目信息列表
        """
        completed_projects = []

        try:
            project_path = self.config.get('project_path', './projects')

            if not os.path.exists(project_path):
                return completed_projects

            # 扫描项目目录
            for item in os.listdir(project_path):
                item_path = os.path.join(project_path, item)

                if os.path.isdir(item_path):
                    # 检查是否有转录文件（原始JSON或parsed.json都算）
                    has_transcription = False
                    has_parsed_cache = False
                    original_json_count = 0
                    parsed_json_count = 0

                    for file in os.listdir(item_path):
                        if file.endswith('.json'):
                            if any(service in file.lower() for service in ['elevenlabs', 'deepgram', 'assemblyai']):
                                has_transcription = True
                                original_json_count += 1
                            elif file.endswith('-parsed.json'):
                                has_parsed_cache = True
                                parsed_json_count += 1
                                # 关键修复：parsed.json也算有转录！
                                has_transcription = True

                    if has_transcription:
                        completed_projects.append({
                            'path': item_path,
                            'name': item,
                            'has_cache': has_parsed_cache,
                            'original_json_count': original_json_count,
                            'parsed_json_count': parsed_json_count,
                            'status': 'parsed_only' if original_json_count == 0 and parsed_json_count > 0 else 'complete'
                        })

            return completed_projects

        except Exception as e:
            self.log_message(f"扫描项目文件夹失败: {str(e)}")
            return completed_projects

    def _generate_subtitles_sync(self, task, task_index):
        """字幕生成主流程 - 支持分段生成和自动合并

        处理流程：
        1. 检测是否为分段处理
        2. 生成分段字幕文件
        3. 分析合并准备状态
        4. 执行字幕合并
        5. 验证最终结果
        """
        try:
            task_name = task.get('name', '未知任务')
            input_file = task.get('path', '')

            if not input_file:
                self.log_message(f"[ERROR] 任务路径为空: {task_name}")
                return False

            # 获取任务文件夹和基础文件名
            task_folder = self._get_task_project_folder(input_file)
            base_filename = os.path.splitext(os.path.basename(input_file))[0]

            # 检查取消状态
            if self.conversion_cancelled:
                return False

            self.log_message(f"[INFO] 开始字幕生成流程: {task_name}")

            # === 阶段1：分段字幕生成 (40-85%) ===
            segment_results = self._generate_segments_with_progress(task_folder, base_filename, task_index)

            if not segment_results['success'] and not segment_results['partial_success']:
                self.log_message(f"[ERROR] 分段字幕生成完全失败: {task_name}")
                self._update_task_progress(task_index, custom_status='字幕生成失败: 分段生成失败')
                return False

            if segment_results['partial_success'] and not segment_results['success']:
                self.log_message(f"[WARNING] 分段字幕生成部分失败: {segment_results['failed_count']}/{segment_results['total_count']}")

            # 检查取消状态
            if self.conversion_cancelled:
                return False

            # === 阶段2：合并准备检查 (85-87%) ===
            self.log_message(f"[INFO] 检查合并准备状态: {task_name}")
            self._update_task_progress(task_index, force_progress=85)

            merge_groups = self._analyze_merge_readiness(task_folder, base_filename, segment_results)

            if not merge_groups:
                self.log_message(f"[WARNING] 没有可合并的字幕组合，保留分段文件: {task_name}")
                # 降级处理：复制分段文件到输出目录
                self._copy_subtitles_to_output(task_folder, base_filename)
                self._mark_task_fallback_complete(task_index, segment_results)
                return True

            # === 阶段3：执行合并 (87-95%) ===
            self.log_message(f"[INFO] 开始字幕合并: {len(merge_groups)} 个组合")
            merge_results = self._merge_with_progress(task_folder, base_filename, merge_groups, task_index)

            # 检查取消状态
            if self.conversion_cancelled:
                return False

            # === 阶段4：验证和完成 (95-100%) ===
            self.log_message(f"[INFO] 验证合并结果: {task_name}")
            self._update_task_progress(task_index, force_progress=95)

            final_results = self._validate_and_finalize(task_folder, base_filename, merge_results)

            # 复制最终字幕文件到输出目录
            self._copy_subtitles_to_output(task_folder, base_filename)

            # 设置任务完成状态
            if task_index is not None and 0 <= task_index < len(self.task_list):
                self.task_list[task_index]['current_stage'] = 'completed'

            self._update_task_progress(task_index, force_progress=100)

            # 记录最终状态
            if final_results['success']:
                self.log_message(f"[SUCCESS] 字幕生成完成: {task_name} (合并: {final_results['merge_success_count']}/{final_results['total_combinations']})")
            elif final_results['partial_success']:
                self.log_message(f"[PARTIAL] 字幕生成部分完成: {task_name} (合并: {final_results['merge_success_count']}/{final_results['total_combinations']})")
            else:
                self.log_message(f"[FALLBACK] 字幕生成降级完成: {task_name} (保留分段文件)")

            return True

        except Exception as e:
            self.log_message(f"[ERROR] 字幕生成异常: {task_name} - {str(e)}")
            self._update_task_progress(task_index, custom_status="字幕生成异常")
            return False

    def _generate_segments_with_progress(self, task_folder, base_filename, task_index):
        """带进度控制的分段字幕生成 (40-85%)"""
        try:
            results = {
                'success': False,
                'partial_success': False,
                'total_count': 0,
                'success_count': 0,
                'failed_count': 0,
                'failed_files': [],
                'segment_results': {}
            }

            # 获取转录文件
            transcription_files = self._get_transcription_files(task_folder, base_filename)
            if not transcription_files:
                self.log_message(f"[ERROR] 未找到转录文件")
                return results

            results['total_count'] = len(transcription_files)
            self.log_message(f"[INFO] 开始生成分段字幕: {results['total_count']} 个文件")

            # 处理每个转录文件
            for i, parsed_file in enumerate(transcription_files):
                if self.conversion_cancelled:
                    break

                # 计算进度：40% + (45% * i / total_count)
                current_progress = 40 + int(45 * i / results['total_count'])
                self._update_task_progress(task_index, force_progress=current_progress)

                self.log_message(f"[INFO] 处理转录文件 ({i+1}/{results['total_count']}): {os.path.basename(parsed_file)}")

                try:
                    success = self._process_single_transcription_file(parsed_file, task_folder, base_filename)
                    results['segment_results'][parsed_file] = success

                    if success:
                        results['success_count'] += 1
                    else:
                        results['failed_count'] += 1
                        results['failed_files'].append(parsed_file)
                        self.log_message(f"[ERROR] 字幕生成失败: {os.path.basename(parsed_file)}")

                except Exception as e:
                    results['failed_count'] += 1
                    results['failed_files'].append(parsed_file)
                    results['segment_results'][parsed_file] = False
                    self.log_message(f"[ERROR] 处理转录文件异常: {os.path.basename(parsed_file)} - {e}")

            # 计算结果
            results['success'] = results['failed_count'] == 0 and results['success_count'] > 0
            results['partial_success'] = results['success_count'] > 0

            self.log_message(f"[INFO] 分段生成完成: 成功 {results['success_count']}, 失败 {results['failed_count']}")

            return results

        except Exception as e:
            self.log_message(f"[ERROR] 分段生成异常: {e}")
            return {
                'success': False, 'partial_success': False, 'total_count': 0,
                'success_count': 0, 'failed_count': 0, 'failed_files': [], 'segment_results': {}
            }

    def _analyze_merge_readiness(self, task_folder, base_filename, segment_results):
        """智能分析哪些组合可以进行合并"""
        try:
            merge_groups = {}
            enabled_asr_services = self._get_enabled_asr_services()
            enabled_llm_apis = self._get_enabled_llm_apis()

            self.log_message(f"[INFO] 分析合并准备状态: {len(enabled_asr_services)} ASR × {len(enabled_llm_apis)} LLM")

            # 检查是否为分段处理
            from .segmentation_utils import SegmentationUtils
            segment_files = SegmentationUtils.find_segment_files(task_folder, base_filename)

            if not segment_files:
                # 非分段处理：检查单文件字幕
                return self._analyze_single_file_merge_readiness(task_folder, base_filename, enabled_asr_services, enabled_llm_apis)

            # 分段处理：按ASR-LLM组合分组检查
            for asr_service in enabled_asr_services:
                for llm_api in enabled_llm_apis:
                    group_key = f"{asr_service}-{llm_api}"

                    # 查找该组合的所有分段文件
                    combination_segments = self._find_segment_files_for_combination(
                        task_folder, base_filename, asr_service, llm_api, segment_files
                    )

                    # 检查完整性
                    if self._is_combination_complete_for_merge(combination_segments, segment_results):
                        # 获取详细的无效分段信息用于日志
                        invalid_info = self._count_missing_segments(combination_segments, segment_results)

                        merge_groups[group_key] = {
                            'asr_service': asr_service,
                            'llm_api': llm_api,
                            'segment_files': combination_segments,
                            'file_count': len(combination_segments)
                        }

                        if invalid_info['total_invalid'] > 0:
                            self.log_message(f"[READY] {group_key} 准备合并 ({len(combination_segments)} 个分段，{invalid_info['total_invalid']} 个无效)")
                            self.log_message(f"[INFO] 无效分段: {', '.join(invalid_info['details'])}")
                        else:
                            self.log_message(f"[READY] {group_key} 准备合并 ({len(combination_segments)} 个分段)")
                    else:
                        invalid_info = self._count_missing_segments(combination_segments, segment_results)
                        self.log_message(f"[SKIP] {group_key} 无效分段过多 ({invalid_info['total_invalid']} > 2)，跳过合并")
                        if invalid_info['details']:
                            self.log_message(f"[INFO] 无效分段: {', '.join(invalid_info['details'])}")

            return merge_groups

        except Exception as e:
            self.log_message(f"[ERROR] 分析合并准备状态异常: {e}")
            return {}

    def _find_segment_files_for_combination(self, task_folder, base_filename, asr_service, llm_api, segment_files):
        """查找特定ASR-LLM组合的所有分段字幕文件

        使用正则表达式匹配文件名模式：
        {base_filename}_part{N}_{HH}-{MM}-{SS}-{asr_service}-{llm_api}.srt

        Returns:
            list: 包含文件名、路径、大小信息的字典列表，按文件名排序
        """
        try:
            combination_files = []

            # 构建分段字幕文件的模式
            # 格式：{base_filename}_part{N}_{time}-{asr_service}-{llm_api}.srt
            import re
            pattern = re.compile(rf'{re.escape(base_filename)}_part\d+_\d{{2}}-\d{{2}}-\d{{2}}-{re.escape(asr_service)}-{re.escape(llm_api)}\.srt$')

            # 扫描项目目录
            for filename in os.listdir(task_folder):
                if pattern.match(filename):
                    file_path = os.path.join(task_folder, filename)
                    if os.path.exists(file_path):
                        combination_files.append({
                            'filename': filename,
                            'path': file_path,
                            'size': os.path.getsize(file_path)
                        })

            # 按文件名排序（确保part顺序正确）
            combination_files.sort(key=lambda x: x['filename'])

            return combination_files

        except Exception as e:
            self.log_message(f"[ERROR] 查找组合分段文件异常: {e}")
            return []

    def _is_combination_complete_for_merge(self, combination_segments, segment_results):
        """检查组合是否完整可合并 - 支持最多2个无效分段的容忍度"""
        try:
            if not combination_segments:
                return False

            total_segments = len(combination_segments)
            invalid_count = 0
            valid_count = 0

            # 统计有效和无效分段
            for segment_info in combination_segments:
                file_path = segment_info['path']

                # 检查文件是否存在且非空
                if not os.path.exists(file_path) or segment_info['size'] == 0:
                    invalid_count += 1
                else:
                    valid_count += 1

            # 容忍度：最多允许2个无效分段，且必须有有效内容
            MAX_ALLOWED_INVALID = 2

            return invalid_count <= MAX_ALLOWED_INVALID and valid_count > 0

        except Exception as e:
            self.log_message(f"[ERROR] 检查组合完整性异常: {e}")
            return False

    def _count_missing_segments(self, combination_segments, segment_results):
        """详细统计无效分段信息"""
        try:
            missing_count = 0
            empty_count = 0
            details = []

            for segment_info in combination_segments:
                file_path = segment_info['path']
                filename = segment_info['filename']

                if not os.path.exists(file_path):
                    missing_count += 1
                    details.append(f"{filename}(缺失)")
                elif segment_info['size'] == 0:
                    empty_count += 1
                    details.append(f"{filename}(空文件)")

            return {
                'total_invalid': missing_count + empty_count,
                'missing': missing_count,
                'empty': empty_count,
                'details': details
            }
        except Exception:
            return {
                'total_invalid': len(combination_segments),
                'missing': len(combination_segments),
                'empty': 0,
                'details': ['统计异常']
            }

    def _merge_with_progress(self, task_folder, base_filename, merge_groups, task_index):
        """带进度控制的字幕合并 (87-95%)"""
        try:
            results = {
                'success': False,
                'partial_success': False,
                'total_count': len(merge_groups),
                'success_count': 0,
                'failed_count': 0,
                'failed_groups': [],
                'merged_files': []
            }

            if not merge_groups:
                return results

            self.log_message(f"[INFO] 开始执行字幕合并: {results['total_count']} 个组合")

            # 导入合并服务
            from services.subtitle.merger_service import SubtitleMergerService
            merger = SubtitleMergerService()

            # 处理每个合并组
            for i, (group_key, group_info) in enumerate(merge_groups.items()):
                if self.conversion_cancelled:
                    break

                # 计算进度：87% + (8% * i / total_count)
                current_progress = 87 + int(8 * i / results['total_count'])
                self._update_task_progress(task_index, force_progress=current_progress)

                self.log_message(f"[INFO] 合并组合 ({i+1}/{results['total_count']}): {group_key}")

                try:
                    # 准备分段文件列表（使用文件名而不是完整路径）
                    segment_filenames = [seg['filename'] for seg in group_info['segment_files']]

                    # 生成输出文件路径
                    output_filename = f"{base_filename}-{group_info['asr_service']}-{group_info['llm_api']}.srt"
                    output_path = os.path.join(task_folder, output_filename)

                    # 执行合并
                    success = merger.merge_subtitle_group(segment_filenames, task_folder, output_path)

                    if success:
                        results['success_count'] += 1
                        results['merged_files'].append({
                            'group_key': group_key,
                            'output_path': output_path,
                            'output_filename': output_filename,
                            'segment_count': len(segment_filenames)
                        })
                        self.log_message(f"[SUCCESS] {group_key} 合并完成: {output_filename}")
                    else:
                        results['failed_count'] += 1
                        results['failed_groups'].append(group_key)
                        self.log_message(f"[ERROR] {group_key} 合并失败")

                except Exception as e:
                    results['failed_count'] += 1
                    results['failed_groups'].append(group_key)
                    self.log_message(f"[ERROR] {group_key} 合并异常: {e}")

            # 计算结果
            results['success'] = results['failed_count'] == 0 and results['success_count'] > 0
            results['partial_success'] = results['success_count'] > 0

            self.log_message(f"[INFO] 字幕合并完成: 成功 {results['success_count']}, 失败 {results['failed_count']}")

            return results

        except Exception as e:
            self.log_message(f"[ERROR] 字幕合并异常: {e}")
            return {
                'success': False, 'partial_success': False, 'total_count': 0,
                'success_count': 0, 'failed_count': 0, 'failed_groups': [], 'merged_files': []
            }

    def _validate_and_finalize(self, task_folder, base_filename, merge_results):
        """验证合并结果并完成处理 (95-100%)"""
        try:
            final_results = {
                'success': False,
                'partial_success': False,
                'total_combinations': merge_results['total_count'],
                'merge_success_count': merge_results['success_count'],
                'validation_issues': []
            }

            if merge_results['success_count'] == 0:
                self.log_message(f"[WARNING] 没有成功的合并文件需要验证")
                return final_results

            # 验证每个合并文件
            valid_files = 0
            for merged_file_info in merge_results['merged_files']:
                try:
                    output_path = merged_file_info['output_path']

                    # 检查文件存在性和大小
                    if not os.path.exists(output_path):
                        final_results['validation_issues'].append(f"合并文件不存在: {merged_file_info['output_filename']}")
                        continue

                    file_size = os.path.getsize(output_path)
                    if file_size == 0:
                        final_results['validation_issues'].append(f"合并文件为空: {merged_file_info['output_filename']}")
                        continue

                    # 基本格式验证
                    if self._validate_merged_subtitle_file(output_path):
                        valid_files += 1
                        self.log_message(f"[VALID] {merged_file_info['output_filename']} ({file_size} 字节)")
                    else:
                        final_results['validation_issues'].append(f"合并文件格式无效: {merged_file_info['output_filename']}")

                except Exception as e:
                    final_results['validation_issues'].append(f"验证异常 {merged_file_info['output_filename']}: {e}")

            # 计算最终结果
            final_results['success'] = valid_files == merge_results['success_count'] and valid_files > 0
            final_results['partial_success'] = valid_files > 0
            final_results['valid_files_count'] = valid_files

            # 记录验证结果
            if final_results['success']:
                self.log_message(f"[SUCCESS] 所有合并文件验证通过: {valid_files} 个文件")
            elif final_results['partial_success']:
                self.log_message(f"[PARTIAL] 部分合并文件验证通过: {valid_files}/{merge_results['success_count']} 个文件")
                for issue in final_results['validation_issues']:
                    self.log_message(f"[ISSUE] {issue}")
            else:
                self.log_message(f"[ERROR] 所有合并文件验证失败")
                for issue in final_results['validation_issues']:
                    self.log_message(f"[ISSUE] {issue}")

            return final_results

        except Exception as e:
            self.log_message(f"[ERROR] 验证和完成处理异常: {e}")
            return {
                'success': False, 'partial_success': False, 'total_combinations': 0,
                'merge_success_count': 0, 'validation_issues': [f"验证异常: {e}"]
            }

    def _validate_merged_subtitle_file(self, file_path):
        """验证合并字幕文件的有效性"""
        try:
            if not os.path.exists(file_path) or os.path.getsize(file_path) == 0:
                return False

            # 检查文件格式
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 基本SRT格式验证
            import re
            entries = re.findall(r'^\d+$', content, re.MULTILINE)
            timecodes = re.findall(r'\d{2}:\d{2}:\d{2},\d{3}', content)

            # 至少要有一些字幕条目
            return len(entries) > 0 and len(timecodes) >= 2

        except Exception:
            return False

    def _mark_task_fallback_complete(self, task_index, segment_results):
        """标记任务为降级完成（保留分段文件）"""
        try:
            if task_index is not None and 0 <= task_index < len(self.task_list):
                self.task_list[task_index]['current_stage'] = 'completed'
                # 可以添加特殊标记表示这是降级完成
                self.task_list[task_index]['completion_type'] = 'fallback'
        except Exception as e:
            self.log_message(f"[ERROR] 标记降级完成异常: {e}")

    def _analyze_single_file_merge_readiness(self, task_folder, base_filename, enabled_asr_services, enabled_llm_apis):
        """分析单文件（非分段）的合并准备状态"""
        try:
            merge_groups = {}

            # 对于单文件处理，每个ASR-LLM组合对应一个字幕文件
            for asr_service in enabled_asr_services:
                for llm_api in enabled_llm_apis:
                    group_key = f"{asr_service}-{llm_api}"

                    # 检查单个字幕文件
                    subtitle_file = f"{base_filename}-{asr_service}-{llm_api}.srt"
                    subtitle_path = os.path.join(task_folder, subtitle_file)

                    if os.path.exists(subtitle_path) and os.path.getsize(subtitle_path) > 0:
                        # 单文件已存在，无需合并，但记录为"已准备"
                        merge_groups[group_key] = {
                            'asr_service': asr_service,
                            'llm_api': llm_api,
                            'segment_files': [{'path': subtitle_path, 'filename': subtitle_file, 'size': os.path.getsize(subtitle_path)}],
                            'file_count': 1,
                            'is_single_file': True
                        }
                        self.log_message(f"[READY] {group_key} 单文件已存在: {subtitle_file}")

            return merge_groups

        except Exception as e:
            self.log_message(f"[ERROR] 分析单文件合并准备状态异常: {e}")
            return {}

    def _get_transcription_files(self, task_folder, base_filename):
        """获取转录文件列表"""
        transcription_files = []
        try:
            for file in os.listdir(task_folder):
                if file.startswith(base_filename) and file.endswith('-parsed.json'):
                    transcription_files.append(file)
        except Exception as e:
            self.log_message(f"[ERROR] 扫描转录文件失败: {str(e)}")
        return transcription_files

    def _process_single_transcription_file(self, parsed_file, task_folder, base_filename):
        """处理单个转录文件生成字幕"""
        try:
            # 读取转录结果
            parsed_file_path = os.path.join(task_folder, parsed_file)

            # 调用LLM服务生成字幕
            result = self._call_llm_service_sync(parsed_file_path)

            if result and result.get('success'):
                self.log_message(f"[SUCCESS] {parsed_file} 字幕生成成功")
                return True
            else:
                error_msg = result.get('error', '未知错误') if result else '生成失败'
                self.log_message(f"[ERROR] {parsed_file} 字幕生成失败: {error_msg}")
                return False

        except Exception as e:
            self.log_message(f"[ERROR] 处理转录文件失败: {parsed_file} - {str(e)}")
            return False

    def _call_llm_service_sync(self, parsed_file_path):
        """调用LLM服务生成字幕（同步执行）"""
        try:
            # 导入字幕生成服务
            from services.subtitle.llm_service import SubtitleLLMService

            # 创建字幕生成服务实例
            subtitle_service = SubtitleLLMService()

            # 调用字幕生成服务
            result = subtitle_service.generate_subtitle(parsed_file_path)

            return result

        except Exception as e:
            self.log_message(f"[ERROR] LLM服务调用失败: {str(e)}")
            return {'success': False, 'error': str(e)}

    def _copy_subtitles_to_output(self, task_folder, base_filename):
        """复制字幕文件到输出目录

        注意：此方法只负责文件复制，不执行合并。
        合并功能由新的字幕生成流程统一处理。
        """
        try:
            # 1. 查找生成的字幕文件（只复制最终合并文件，排除分段文件）
            subtitle_files = []
            for file in os.listdir(task_folder):
                if (file.startswith(base_filename) and
                    file.endswith('.srt') and
                    '_part' not in file):
                    subtitle_files.append(file)

            if not subtitle_files:
                self.log_message(f"[WARNING] 未找到生成的字幕文件: {base_filename}")
                return

            # 2. 复制到字幕目录
            subtitle_folder = self.config.get('subtitle_folder', './subtitles')
            os.makedirs(subtitle_folder, exist_ok=True)

            for subtitle_file in subtitle_files:
                src_path = os.path.join(task_folder, subtitle_file)
                dst_path = os.path.join(subtitle_folder, subtitle_file)

                import shutil
                shutil.copy2(src_path, dst_path)
                self.log_message(f"[COPY] 字幕文件已复制: {subtitle_file}")

            # 3. 验证ASR-LLM组合完整性
            status = self._analyze_asr_llm_combinations_status(task_folder, base_filename)

            if status:
                self.log_message(f"[INFO] ASR-LLM组合完成度: {status['subtitle_ready']}/{status['total_combinations']}")

                if status['missing_subtitles']:
                    missing_info = [f"{m['asr_service']}-{m['llm_api']}" for m in status['missing_subtitles']]
                    self.log_message(f"[INFO] 缺失的组合: {', '.join(missing_info)}")

        except Exception as e:
            self.log_message(f"[WARNING] 复制字幕文件失败: {str(e)}")


    def _create_task_from_folder(self, task_folder, base_filename=None):
        """从任务文件夹创建task对象"""
        try:
            # 如果没有提供base_filename，从文件夹中推断
            if not base_filename:
                # 查找音频文件来推断原始文件名
                for file in os.listdir(task_folder):
                    if file.endswith(('.wav', '.flac', '.mp3', '.m4a')):
                        base_filename = os.path.splitext(file)[0]
                        break

                # 如果没有音频文件，从parsed.json推断
                if not base_filename:
                    for file in os.listdir(task_folder):
                        if file.endswith('-parsed.json'):
                            # 从 "filename-service-parsed.json" 提取 "filename"
                            parts = file.replace('-parsed.json', '').split('-')
                            if len(parts) >= 2:
                                base_filename = '-'.join(parts[:-1])  # 去掉最后的service部分
                                break

                # 如果还是没有，使用文件夹名
                if not base_filename:
                    base_filename = os.path.basename(task_folder)

            # 构造原始文件路径（推测）
            # 这里我们需要推测原始文件的路径，因为task_folder是项目文件夹
            original_file_path = task_folder  # 使用文件夹路径作为默认值

            # 尝试找到原始文件的实际路径
            for file in os.listdir(task_folder):
                if file.startswith(base_filename) and not file.endswith(('.json', '.srt')):
                    original_file_path = os.path.join(task_folder, file)
                    break

            task = {
                'name': base_filename,
                'path': original_file_path,
                'size': 0  # 默认值
            }

            return task

        except Exception as e:
            self.log_message(f"[ERROR] 从文件夹创建task对象失败: {str(e)}")
            # 返回最基本的task对象
            return {
                'name': os.path.basename(task_folder),
                'path': task_folder,
                'size': 0
            }

    def _create_task_from_project(self, project):
        """从项目信息创建task对象"""
        try:
            project_path = project['path']
            project_name = project['name']

            # 查找原始文件
            original_file_path = project_path
            for file in os.listdir(project_path):
                if not file.endswith(('.json', '.srt')) and os.path.isfile(os.path.join(project_path, file)):
                    original_file_path = os.path.join(project_path, file)
                    break

            task = {
                'name': project_name,
                'path': original_file_path,
                'size': 0  # 默认值
            }

            return task

        except Exception as e:
            self.log_message(f"[ERROR] 从项目创建task对象失败: {str(e)}")
            # 返回最基本的task对象
            return {
                'name': project.get('name', '未知项目'),
                'path': project.get('path', ''),
                'size': 0
            }

    def _run_elevenlabs_transcription(self, audio_path: str, original_file_path: str, task_name: str) -> bool:
        """运行ElevenLabs转录"""
        # 应用层重试逻辑（2次重试）
        max_app_retries = 2

        for app_attempt in range(max_app_retries + 1):
            try:
                if app_attempt > 0:
                    wait_time = 3 * app_attempt  # 3, 6秒
                    self.log_message(f"[应用层重试] ElevenLabs转录重试第{app_attempt}次: {task_name}，等待{wait_time}秒...")
                    time.sleep(wait_time)

                # 使用同步ASR服务
                from services.asr import create_asr_service, ASRRequest

                # 直接获取ASR服务配置（无需重新构建）
                asr_config = self.config_manager.get_asr_service_config('elevenlabs')

                # 根据模式验证API Key
                mode = asr_config.get('mode', 'free')
                if mode == 'api':
                    # API模式需要API Key
                    if not asr_config.get('api_key', '').strip():
                        self.log_message("ElevenLabs API模式需要API Key，跳过转录")
                        return False
                elif mode == 'free':
                    # 免费模式不需要API Key验证
                    pass
                else:
                    self.log_message(f"ElevenLabs未知模式: {mode}，跳过转录")
                    return False

                # 创建ASR服务实例
                asr_service = create_asr_service('elevenlabs', asr_config)

                # 获取转录模式
                mode = asr_config.get('mode', 'api')
                self.log_message(f"开始ElevenLabs {mode}模式转录: {task_name} (应用层尝试 {app_attempt + 1}/{max_app_retries + 1})")

                # 创建转录请求（直接使用配置）
                asr_request = ASRRequest(
                    audio_file_path=audio_path,
                    language=asr_config.get('language', 'auto'),
                    enable_diarization=asr_config.get('enable_diarization', True),
                    enable_punctuation=True,
                    enable_timestamps=True,
                    num_speakers=asr_config.get('num_speakers', 0) or None
                )

                # 执行同步转录
                asr_response = asr_service.transcribe(asr_request)

                # 检查转录结果
                if asr_response.status.value == 'completed' and asr_response.full_text:
                    # 转录成功，保存转录结果
                    result_data = {
                        'success': True,
                        'service': 'elevenlabs',
                        'transcription_id': asr_response.transcription_id,
                        'text': asr_response.full_text,
                        'words': asr_response.words,
                        'language_detected': asr_response.language_detected,
                        'confidence': asr_response.confidence,
                        'processing_time': asr_response.processing_time,
                        'metadata': asr_response.metadata,
                        'created_at': asr_response.created_at
                    }

                    # 保存JSON文件
                    original_file_name = os.path.splitext(os.path.basename(original_file_path))[0]
                    task_folder = os.path.dirname(audio_path)
                    json_file_path = os.path.join(task_folder, f"{original_file_name}-ElevenLabs.json")

                    with open(json_file_path, 'w', encoding='utf-8') as f:
                        json.dump(result_data, f, ensure_ascii=False, indent=2)

                    self.log_message(f"ElevenLabs转录完成: {task_name} (应用层尝试 {app_attempt + 1})")
                    self.log_message(f"JSON文件已保存: {json_file_path}")
                    return True
                else:
                    error_msg = asr_response.error_message or '转录失败'
                    raise Exception(f"转录状态异常: {error_msg}")

            except Exception as e:
                error_str = str(e)
                self.log_message(f"ElevenLabs转录失败 (应用层尝试 {app_attempt + 1}/{max_app_retries + 1}): {task_name} - {error_str}")

                # 判断是否值得重试
                if any(keyword in error_str.lower() for keyword in ['api key', 'quota', 'unauthorized', 'forbidden']):
                    self.log_message(f"ElevenLabs转录遇到不可重试错误，停止重试: {error_str}")
                    return False

                # 最后一次尝试失败
                if app_attempt == max_app_retries:
                    self.log_message(f"ElevenLabs转录最终失败: {task_name}")
                    return False

        return False


    def _run_assemblyai_transcription(self, audio_path: str, original_file_path: str, task_name: str) -> bool:
        """运行AssemblyAI转录"""
        # 应用层重试逻辑（2次重试）
        max_app_retries = 2

        for app_attempt in range(max_app_retries + 1):
            try:
                if app_attempt > 0:
                    wait_time = 3 * app_attempt  # 3, 6秒
                    self.log_message(f"[应用层重试] AssemblyAI转录重试第{app_attempt}次: {task_name}，等待{wait_time}秒...")
                    time.sleep(wait_time)

                # 使用同步ASR服务
                from services.asr import create_asr_service, ASRRequest

                # 直接获取ASR服务配置（无需重新构建）
                asr_config = self.config_manager.get_asr_service_config('assemblyai')

                # 验证API Key
                if not asr_config.get('api_key', '').strip():
                    self.log_message("AssemblyAI API Key为空，跳过转录")
                    return False

                # 创建ASR服务实例
                asr_service = create_asr_service('assemblyai', asr_config)

                self.log_message(f"开始AssemblyAI转录: {task_name} (应用层尝试 {app_attempt + 1}/{max_app_retries + 1})")

                # 创建转录请求（直接使用配置）
                asr_request = ASRRequest(
                    audio_file_path=audio_path,
                    language=asr_config.get('language_code', 'auto'),
                    enable_diarization=asr_config.get('speaker_labels', True),
                    enable_punctuation=asr_config.get('punctuate', True),
                    enable_timestamps=True,
                    num_speakers=asr_config.get('speakers_expected', 0) or None
                )

                # 执行同步转录
                asr_response = asr_service.transcribe(asr_request)

                # 检查转录结果
                if asr_response.status.value == 'completed' and asr_response.full_text:
                    # 转录成功，保存转录结果
                    result_data = {
                        'success': True,
                        'service': 'assemblyai',
                        'transcription_id': asr_response.transcription_id,
                        'text': asr_response.full_text,
                        'words': asr_response.words,
                        'language_detected': asr_response.language_detected,
                        'confidence': asr_response.confidence,
                        'processing_time': asr_response.processing_time,
                        'metadata': asr_response.metadata,
                        'created_at': asr_response.created_at
                    }

                    # 保存JSON文件
                    original_file_name = os.path.splitext(os.path.basename(original_file_path))[0]
                    task_folder = os.path.dirname(audio_path)
                    json_file_path = os.path.join(task_folder, f"{original_file_name}-AssemblyAI.json")

                    with open(json_file_path, 'w', encoding='utf-8') as f:
                        json.dump(result_data, f, ensure_ascii=False, indent=2)

                    self.log_message(f"AssemblyAI转录完成: {task_name} (应用层尝试 {app_attempt + 1})")
                    self.log_message(f"JSON文件已保存: {json_file_path}")
                    return True
                else:
                    error_msg = asr_response.error_message or '转录失败'
                    raise Exception(f"转录状态异常: {error_msg}")

            except Exception as e:
                error_str = str(e)
                self.log_message(f"AssemblyAI转录失败 (应用层尝试 {app_attempt + 1}/{max_app_retries + 1}): {task_name} - {error_str}")

                # 判断是否值得重试
                if any(keyword in error_str.lower() for keyword in ['api key', 'quota', 'unauthorized', 'forbidden']):
                    self.log_message(f"AssemblyAI转录遇到不可重试错误，停止重试: {error_str}")
                    return False

                # 最后一次尝试失败
                if app_attempt == max_app_retries:
                    self.log_message(f"AssemblyAI转录最终失败: {task_name}")
                    return False

        return False

    def _run_deepgram_transcription(self, audio_path: str, original_file_path: str, task_name: str) -> bool:
        """运行Deepgram转录"""
        # 应用层重试逻辑（2次重试）
        max_app_retries = 2

        for app_attempt in range(max_app_retries + 1):
            try:
                if app_attempt > 0:
                    wait_time = 3 * app_attempt  # 3, 6秒
                    self.log_message(f"[应用层重试] Deepgram转录重试第{app_attempt}次: {task_name}，等待{wait_time}秒...")
                    time.sleep(wait_time)

                # 使用同步ASR服务
                from services.asr import create_asr_service, ASRRequest

                # 直接获取ASR服务配置（无需重新构建）
                asr_config = self.config_manager.get_asr_service_config('deepgram')

                # 验证API Key
                if not asr_config.get('api_key', '').strip():
                    self.log_message("Deepgram API Key为空，跳过转录")
                    return False

                # 创建ASR服务实例
                asr_service = create_asr_service('deepgram', asr_config)

                self.log_message(f"开始Deepgram转录: {task_name} (应用层尝试 {app_attempt + 1}/{max_app_retries + 1})")

                # 创建转录请求（直接使用配置）
                asr_request = ASRRequest(
                    audio_file_path=audio_path,
                    language=asr_config.get('language', 'auto'),
                    enable_diarization=asr_config.get('enable_diarization', True),
                    enable_punctuation=asr_config.get('enable_punctuation', True),
                    enable_timestamps=True
                )

                # 执行同步转录
                asr_response = asr_service.transcribe(asr_request)

                # 检查转录结果
                if asr_response.status.value == 'completed' and asr_response.full_text:
                    # 转录成功，保存转录结果
                    result_data = {
                        'success': True,
                        'service': 'deepgram',
                        'transcription_id': asr_response.transcription_id,
                        'text': asr_response.full_text,
                        'words': asr_response.words,
                        'language_detected': asr_response.language_detected,
                        'confidence': asr_response.confidence,
                        'processing_time': asr_response.processing_time,
                        'metadata': asr_response.metadata,
                        'created_at': asr_response.created_at
                    }

                    # 保存JSON文件
                    original_file_name = os.path.splitext(os.path.basename(original_file_path))[0]
                    task_folder = os.path.dirname(audio_path)
                    json_file_path = os.path.join(task_folder, f"{original_file_name}-Deepgram.json")

                    with open(json_file_path, 'w', encoding='utf-8') as f:
                        json.dump(result_data, f, ensure_ascii=False, indent=2)

                    self.log_message(f"Deepgram转录完成: {task_name} (应用层尝试 {app_attempt + 1})")
                    self.log_message(f"JSON文件已保存: {json_file_path}")
                    return True
                else:
                    error_msg = asr_response.error_message or '转录失败'
                    raise Exception(f"转录状态异常: {error_msg}")

            except Exception as e:
                error_str = str(e)
                self.log_message(f"Deepgram转录失败 (应用层尝试 {app_attempt + 1}/{max_app_retries + 1}): {task_name} - {error_str}")

                # 判断是否值得重试
                if any(keyword in error_str.lower() for keyword in ['api key', 'quota', 'unauthorized', 'forbidden']):
                    self.log_message(f"Deepgram转录遇到不可重试错误，停止重试: {error_str}")
                    return False

                # 最后一次尝试失败
                if app_attempt == max_app_retries:
                    self.log_message(f"Deepgram转录最终失败: {task_name}")
                    return False

        return False


    def _browse_project_directory(self, e):
        """浏览选择项目目录"""

        def on_dialog_result(e):
            if e.path:
                # 设置标志位，避免重复保存
                if hasattr(self.settings_manager, 'project_path_field'):
                    self.settings_manager._is_browsing_project = True
                    self.settings_manager.project_path_field.value = e.path
                    self.settings_manager._is_browsing_project = False

                # 保存配置
                self.config['project_path'] = e.path
                self.config_manager.save_config()
                self.page.update()
                self.log_message(f"已设置项目目录: {e.path}")

        file_picker = ft.FilePicker(on_result=on_dialog_result)
        self.page.overlay.append(file_picker)
        self.page.update()

        file_picker.get_directory_path(
            dialog_title="选择项目工作目录"
        )

    def _browse_subtitle_directory(self, e):
        """浏览选择字幕目录"""

        def on_dialog_result(e):
            if e.path:
                # 设置标志位，避免重复保存
                if hasattr(self.settings_manager, 'subtitle_path_field'):
                    self.settings_manager._is_browsing_subtitle = True
                    self.settings_manager.subtitle_path_field.value = e.path
                    self.settings_manager._is_browsing_subtitle = False

                # 保存配置
                self.config['subtitle_path'] = e.path
                self.config_manager.save_config()
                self.page.update()
                self.log_message(f"已设置字幕目录: {e.path}")

        file_picker = ft.FilePicker(on_result=on_dialog_result)
        self.page.overlay.append(file_picker)
        self.page.update()

        file_picker.get_directory_path(
            dialog_title="选择字幕目录"
        )


    def _open_project_directory(self, e):
        """打开项目目录"""
        import subprocess
        import platform

        project_path = self.config.get('project_path', './projects')

        try:
            # 转换为绝对路径
            abs_project_path = os.path.abspath(project_path)

            # 确保目录存在
            if not os.path.exists(abs_project_path):
                self.log_message(f"项目目录不存在，正在创建: {abs_project_path}")
                os.makedirs(abs_project_path, exist_ok=True)
                self.log_message(f"项目目录创建成功: {abs_project_path}")

            # 再次检查目录是否存在
            if not os.path.exists(abs_project_path):
                self.log_message(f"无法创建项目目录: {abs_project_path}")
                return

            # 根据操作系统打开文件夹
            if platform.system() == "Windows":
                os.startfile(abs_project_path)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", abs_project_path], encoding='utf-8', errors='ignore')
            else:  # Linux
                subprocess.run(["xdg-open", abs_project_path], encoding='utf-8', errors='ignore')

            self.log_message(f"已打开项目目录: {abs_project_path}")
        except Exception as ex:
            self.log_message(f"打开项目目录失败: {ex}")
            # 提供备用方案：显示路径信息
            try:
                abs_path = os.path.abspath(project_path)
                self.log_message(f"项目目录路径: {abs_path}")
                self.log_message(f"当前工作目录: {os.getcwd()}")
            except:
                pass

    def _get_task_project_folder(self, file_path):
        """为任务文件获取项目子文件夹路径"""
        # 获取文件名（不包括扩展名）
        file_name = os.path.splitext(os.path.basename(file_path))[0]

        # 构建子文件夹路径
        project_path = self.config.get('project_path', './projects')
        task_folder = os.path.join(project_path, file_name)

        # 确保子文件夹存在
        try:
            os.makedirs(task_folder, exist_ok=True)
        except Exception as ex:
            self.log_message(f"创建任务文件夹失败: {ex}")
            return None

        return task_folder


    # 移除复杂的颜色更新方法，改为重新创建UI

    def _add_files(self, e):
        """添加音视频文件到任务列表"""

        def on_dialog_result(e):
            if e.files and len(e.files) > 0:
                for file in e.files:
                    self._add_task_item(file.path, file.size if hasattr(file, 'size') else 0)
                self._update_task_list_view()
                self.log_message(f"已添加 {len(e.files)} 个音视频文件到任务列表")

        file_picker = ft.FilePicker(on_result=on_dialog_result)
        self.page.overlay.append(file_picker)
        self.page.update()

        # 支持ffmpeg能处理的所有音视频格式
        audio_video_extensions = [
            # 视频格式
            "mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "m4v", "3gp", "3g2",
            "mpg", "mpeg", "m2v", "m4p", "m4b", "f4v", "f4p", "f4a", "f4b",
            "vob", "ogv", "ogg", "drc", "gif", "gifv", "mng", "avi", "mov", "qt",
            "wmv", "yuv", "rm", "rmvb", "asf", "amv", "mp4", "m4p", "m4v", "mpg",
            "mp2", "mpeg", "mpe", "mpv", "m2v", "svi", "3gp", "3g2", "mxf", "roq",
            "nsv", "flv", "f4v", "f4p", "f4a", "f4b",
            # 音频格式
            "wav", "mp3", "flac", "aac", "ogg", "wma", "m4a", "opus", "aiff", "au",
            "ra", "3ga", "amr", "awb", "dct", "dss", "dvf", "gsm", "iklax", "ivs",
            "m4p", "mmf", "mpc", "msv", "nmf", "oga", "mogg", "raw", "sln", "tta",
            "voc", "vox", "wv", "webm", "8svx", "cda"
        ]

        file_picker.pick_files(
            dialog_title="选择要转换的音视频文件",
            allowed_extensions=audio_video_extensions,
            allow_multiple=True
        )

    def _add_folder(self, e):
        """添加文件夹中的所有音视频文件到任务列表"""

        def on_dialog_result(e):
            if e.path:
                # 支持的音视频文件扩展名
                audio_video_extensions = {
                    # 视频格式
                    '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.3g2',
                    '.mpg', '.mpeg', '.m2v', '.m4p', '.m4b', '.f4v', '.f4p', '.f4a', '.f4b',
                    '.vob', '.ogv', '.ogg', '.drc', '.gif', '.gifv', '.mng', '.qt',
                    '.yuv', '.rm', '.rmvb', '.asf', '.amv', '.mp2', '.mpe', '.mpv', '.svi',
                    '.mxf', '.roq', '.nsv',
                    # 音频格式
                    '.wav', '.mp3', '.flac', '.aac', '.wma', '.m4a', '.opus', '.aiff', '.au',
                    '.ra', '.3ga', '.amr', '.awb', '.dct', '.dss', '.dvf', '.gsm', '.iklax', '.ivs',
                    '.mmf', '.mpc', '.msv', '.nmf', '.oga', '.mogg', '.raw', '.sln', '.tta',
                    '.voc', '.vox', '.wv', '.8svx', '.cda'
                }

                media_files = []
                for root, dirs, files in os.walk(e.path):
                    for file in files:
                        file_ext = os.path.splitext(file)[1].lower()
                        if file_ext in audio_video_extensions:
                            file_path = os.path.join(root, file)
                            try:
                                file_size = os.path.getsize(file_path)
                                media_files.append((file_path, file_size))
                            except OSError:
                                media_files.append((file_path, 0))

                for file_path, file_size in media_files:
                    self._add_task_item(file_path, file_size)

                self._update_task_list_view()
                self.log_message(f"已从文件夹添加 {len(media_files)} 个音视频文件到任务列表")

        file_picker = ft.FilePicker(on_result=on_dialog_result)
        self.page.overlay.append(file_picker)
        self.page.update()

        file_picker.get_directory_path(
            dialog_title="选择包含音视频文件的文件夹"
        )

    def _clear_task_list(self, e):
        """清空任务列表"""
        self.task_list.clear()
        self._update_task_list_view()
        self.log_message("已清空任务列表")

        # 重置按钮状态
        self.start_button.text = "开始"
        self.start_button.disabled = False
        self.cancel_button.disabled = True

        # 重置转换状态
        self.conversion_cancelled = False

        self.page.update()

    def _add_task_item(self, file_path, file_size):
        """添加任务项到列表"""
        # 检查文件是否已存在
        for task in self.task_list:
            if task['path'] == file_path:
                return  # 文件已存在，不重复添加

        # 获取文件信息
        file_name = os.path.basename(file_path)
        file_ext = os.path.splitext(file_name)[1].upper()

        # 格式化文件大小
        if file_size > 1024 * 1024:
            size_str = f"{file_size / (1024 * 1024):.1f} MB"
        elif file_size > 1024:
            size_str = f"{file_size / 1024:.1f} KB"
        else:
            size_str = f"{file_size} B"

        # 添加到任务列表
        task_item = {
            'path': file_path,
            'name': file_name,
            'size': file_size,
            'size_str': size_str,
            'ext': file_ext,
            'progress': 0,
            'status': '等待中'
        }

        self.task_list.append(task_item)

    def _update_task_list_view(self):
        """更新任务列表视图"""
        if not self.task_list_view:
            return

        colors = self.theme_manager.get_theme_colors()
        self.task_list_view.controls.clear()

        for i, task in enumerate(self.task_list):
            # 解析阶段和状态（使用新的统一解析逻辑）
            stage, status_icon = self._parse_task_stage_and_status(task['status'])

            # 苹果简约风格 - 统一使用低调的颜色
            stage_color = colors['text_color']  # 统一使用苹果黑，不再区分阶段颜色

            # 创建任务行
            task_row = ft.Container(
                content=ft.Row([
                    ft.Container(
                        content=ft.Text(
                            task['name'],
                            size=12,
                            color=colors['text_color'],
                            tooltip=task['path'],  # 显示完整路径
                            text_align=ft.TextAlign.CENTER
                        ),
                        expand=2,
                        alignment=ft.alignment.center
                    ),
                    ft.Container(
                        content=ft.Text(
                            task['size_str'],
                            size=12,
                            color=colors['secondary_text_color'],
                            text_align=ft.TextAlign.CENTER
                        ),
                        expand=1,
                        alignment=ft.alignment.center
                    ),
                    ft.Container(
                        content=ft.Text(
                            task['ext'],
                            size=12,
                            color=colors['secondary_text_color'],
                            text_align=ft.TextAlign.CENTER
                        ),
                        expand=1,
                        alignment=ft.alignment.center
                    ),
                    ft.Container(
                        content=ft.Text(
                            stage,
                            size=12,
                            color=stage_color,
                            weight="bold",
                            text_align=ft.TextAlign.CENTER
                        ),
                        expand=1,
                        alignment=ft.alignment.center
                    ),
                    ft.Container(
                        content=ft.Text(
                            status_icon,
                            size=16,
                            text_align=ft.TextAlign.CENTER
                        ),
                        expand=1,
                        alignment=ft.alignment.center
                    ),
                    ft.Container(
                        content=ft.Row([
                            ft.ProgressBar(
                                value=task['progress'] / 100.0,
                                width=50,
                                color=colors['accent_color']  # 雾霾蓝进度条
                            ),
                            ft.Text(f"{task['progress']}%", size=10, color=colors['secondary_text_color'])
                        ], alignment=ft.MainAxisAlignment.CENTER),
                        expand=1,
                        alignment=ft.alignment.center
                    )
                ]),
                padding=ft.padding.symmetric(horizontal=10, vertical=5),
                bgcolor=colors['menu_unselected'] if i % 2 == 0 else "transparent",
                border_radius=4
            )

            self.task_list_view.controls.append(task_row)

        self.page.update()

    def _start_conversion(self, e):
        """开始/继续转换，支持智能检测项目状态"""
        if not self.task_list:
            # 修复：当任务列表为空时，检查是否有已完成的项目可以生成字幕
            self.log_message("任务列表为空，检查是否有已完成的项目...")
            completed_projects = self._get_completed_projects()

            if completed_projects:
                self.log_message(f"发现 {len(completed_projects)} 个已完成转录的项目")

                # 检查是否有只有parsed.json的项目（可以直接生成字幕）
                parsed_only_projects = [p for p in completed_projects if p.get('status') == 'parsed_only']

                if parsed_only_projects:
                    self.log_message(f"发现 {len(parsed_only_projects)} 个可以直接生成字幕的项目")
                    # 为这些项目执行字幕生成
                    for project in parsed_only_projects:
                        self.log_message(f"开始为项目 {project['name']} 生成字幕...")
                        task = self._create_task_from_project(project)
                        success = self._generate_subtitles_sync(task, -1)
                        if success:
                            self.log_message(f"项目 {project['name']} 字幕生成完成")
                        else:
                            self.log_message(f"项目 {project['name']} 字幕生成失败")
                    return
                else:
                    self.log_message("已完成的项目都已生成字幕，请添加新的音视频文件")
                    return
            else:
                self.log_message("没有找到任何项目，请先添加音视频文件")
                return

        # 重置取消标志
        self.conversion_cancelled = False


        # 统一增量处理：所有模式都基于文件状态进行智能增量检测


        # 更新按钮状态
        self.start_button.disabled = True
        self.start_button.text = "进行中..."
        self._update_cancel_button_state()  # 使用新的阶段感知逻辑
        self.page.update()

        # 统一的智能增量处理日志
        button_text = self.start_button.text
        if button_text == "继续":
            self.log_message(f"继续处理 {len(self.task_list)} 个任务（智能增量检测）...")
        else:
            self.log_message(f"开始处理 {len(self.task_list)} 个任务（智能增量检测）...")

        # 启动转换线程
        self.conversion_thread = threading.Thread(target=self._run_conversion_process)
        self.conversion_thread.daemon = True
        self.conversion_thread.start()

    def _cancel_conversion(self, e):
        """EISA智能取消：基于阶段感知的智能取消策略"""

        # 防止重复点击
        if self.cancel_button.disabled:
            return

        # 检查是否有任务在进行
        if not self._is_any_task_in_progress():
            self.log_message("当前没有正在进行的任务")
            return

        # 检查当前阶段是否可以取消
        if not self._is_current_stage_cancellable():
            current_stage = self._get_current_processing_stage()
            if current_stage == CombinationStage.TRANSCRIPTION_PROCESSING:
                self.log_message("转录阶段无法取消，请等待转录完成")
            elif current_stage == CombinationStage.SUBTITLE_PROCESSING:
                self.log_message("字幕生成阶段无法取消，请等待生成完成")
            else:
                self.log_message("当前阶段无法取消")
            return

        # EISA智能取消执行
        try:
            self.log_message("正在取消任务...")

            # 设置取消标志
            self.conversion_cancelled = True

            # 更新当前任务状态
            current_task_index = self._get_current_task_index()
            if current_task_index is not None:
                self._update_task_progress(current_task_index, custom_status="已取消")

            # 清理音频文件（保持现有逻辑）
            self._clean_current_processing_audio_files()

            # 更新按钮状态
            self.cancel_button.disabled = True
            self.cancel_button.text = "取消中..."
            self.update_button_state(immediate=True)
            self._update_cancel_button_state()
            self.page.update()

            self.log_message("任务已取消")

        except Exception as e:
            self.log_message(f"取消任务异常: {e}")

    def _get_current_task_index(self) -> Optional[int]:
        """获取当前正在处理的任务索引"""
        try:
            for i, task in enumerate(self.task_list):
                progress = self.progress_calculator.calculate_progress(task)
                if progress.stage != CombinationStage.COMPLETED:
                    return i
            return None
        except Exception as e:
            self.log_message(f"获取当前任务索引异常: {e}")
            return None

    def get_task_stage(self, task_or_index) -> CombinationStage:
        """EISA阶段检测：基于智能计算的任务阶段获取"""
        try:
            # 处理输入参数
            if isinstance(task_or_index, int):
                if 0 <= task_or_index < len(self.task_list):
                    task = self.task_list[task_or_index]
                else:
                    return CombinationStage.AUDIO_PROCESSING
            else:
                task = task_or_index

            # 使用统一的进度计算器
            progress = self.progress_calculator.calculate_progress(task)
            return progress.stage

        except Exception as e:
            self.log_message(f"获取任务阶段异常: {e}")
            return CombinationStage.AUDIO_PROCESSING

    def _get_current_processing_stage(self) -> Optional[CombinationStage]:
        """EISA当前阶段：获取正在处理的任务阶段（修复取消按钮时机问题）"""
        try:
            if not self._is_any_task_in_progress():
                return None

            # 找到第一个未完成的任务
            for task in self.task_list:
                # 修复：优先检查任务是否正在处理中且没有明确阶段
                # 这种情况通常发生在音频处理刚开始时
                if (task.get('status', '').endswith('进行中') and
                    'current_stage' not in task):
                    # 如果任务正在进行但没有明确阶段，推断为音频处理阶段
                    # 这是最常见的情况：音频处理刚开始，TaskFlowController还没设置阶段
                    return CombinationStage.AUDIO_PROCESSING

                # 使用EISA计算获取阶段
                progress = self.progress_calculator.calculate_progress(task)
                if progress.stage != CombinationStage.COMPLETED:
                    return progress.stage

            return None
        except Exception as e:
            self.log_message(f"获取当前处理阶段异常: {e}")
            return None

    def _is_current_stage_cancellable(self) -> bool:
        """EISA取消检测：基于阶段属性的智能可取消性判断"""
        try:
            current_stage = self._get_current_processing_stage()

            if current_stage is None:
                return False

            # 使用EISA的智能可取消标志
            return current_stage.cancellable
        except Exception as e:
            self.log_message(f"检查阶段可取消性异常: {e}")
            return False

    def _update_cancel_button_state(self):
        """EISA按钮更新：基于智能阶段的取消按钮状态管理"""
        try:
            if not self._is_any_task_in_progress():
                # 没有任务进行中
                self.cancel_button.disabled = True
                self.cancel_button.text = "取消"
                return

            current_stage = self._get_current_processing_stage()

            if current_stage and current_stage.cancellable:
                # 可取消阶段
                self.cancel_button.disabled = False
                self.cancel_button.text = "取消"
            else:
                # 不可取消阶段
                self.cancel_button.disabled = True
                self.cancel_button.text = "取消"

        except Exception as e:
            self.log_message(f"更新取消按钮状态异常: {e}")
            # 异常时保持安全状态
            self.cancel_button.disabled = True
            self.cancel_button.text = "取消"

    def _clean_current_processing_audio_files(self):
        """清理音频处理阶段任务的音频文件（基于文件状态）"""
        try:
            # 查找处于音频处理阶段的任务
            for i, task in enumerate(self.task_list):
                stage = self.get_task_stage(task)
                if stage == CombinationStage.AUDIO_PROCESSING:
                    task_name = task.get('name', '未知任务')
                    input_file = task.get('path', '')

                    if not input_file:
                        continue

                    # 获取任务文件夹
                    task_folder = self._get_task_project_folder(input_file)
                    if not task_folder:
                        continue

                    file_name = os.path.splitext(os.path.basename(input_file))[0]

                    # 直接清理音频文件
                    self._clean_audio_files(task_folder, file_name)
                    self.log_message(f"已清理任务 {task_name} 的音频文件")

                    # 更新任务状态（使用新的统一方法）
                    self._update_task_progress(i)

        except Exception as e:
            self.log_message(f"清理当前任务音频文件失败: {e}")


    def _clean_audio_files(self, task_folder, file_name):
        """清理音频转换阶段的文件（只清理WAV输出文件）"""
        # 只清理WAV输出文件，因为一体化处理器只生成WAV
        audio_file = os.path.join(task_folder, f"{file_name}.wav")
        if os.path.exists(audio_file):
            try:
                os.remove(audio_file)
                self.log_message(f"删除音频文件: {audio_file}")
            except Exception as e:
                self.log_message(f"删除音频文件失败: {audio_file} - {e}")

    def _run_conversion_process(self):
        """运行顺序转换处理流程"""
        total_tasks = len(self.task_list)
        completed_tasks = 0

        self.log_message(f"开始顺序转换，总任务数: {total_tasks}")

        # 顺序处理每个任务
        for i, task in enumerate(self.task_list):
            if self.conversion_cancelled:
                break

            # 跳过已完成的任务，但不跳过失败的任务（用户可能想重试）
            # 使用统一接口进行文件状态判断
            if self.get_task_stage(task) == CombinationStage.COMPLETED:
                completed_tasks += 1
                continue

            try:
                success = self._process_single_task(task, i)
                if success:
                    completed_tasks += 1
                    self.log_message(f"任务完成: {task['name']}")
                else:
                    # 修复：任务失败时，确保设置失败状态
                    if hasattr(self, 'task_flow_controller') and self.task_flow_controller:
                        self.task_flow_controller._update_task_stage(i, CombinationStage.FAILED)
                    self.log_message(f"任务失败: {task['name']}")
            except Exception as e:
                self.log_message(f"任务异常: {task['name']} - {str(e)}")
                # 修复：任务异常时，也设置失败状态（除非是取消导致的）
                if not self.conversion_cancelled:
                    if hasattr(self, 'task_flow_controller') and self.task_flow_controller:
                        self.task_flow_controller._update_task_stage(i, CombinationStage.FAILED)
                # 检查是否因为取消导致的异常
                if self.conversion_cancelled:
                    break

        # 转换完成，恢复按钮状态
        if self.conversion_cancelled:
            self.log_message("转换已取消")
            self._conversion_completed(completed_tasks, total_tasks)
        else:
            self._conversion_completed(completed_tasks, total_tasks)

    def _process_single_task(self, task: Dict[str, Any], task_index: int) -> bool:
        """处理单个任务（纯TaskFlowController版本）

        统一的任务处理入口，完全使用TaskFlowController进行智能处理，
        确保EISA架构的纯洁性和一致性。

        Args:
            task: 任务信息
            task_index: 任务索引

        Returns:
            bool: 任务是否成功处理
        """
        # 快速取消检查
        if self.conversion_cancelled:
            return False

        task_name = task.get('name', 'Unknown')

        try:
            # === 统一处理：完全使用TaskFlowController ===
            # 检查TaskFlowController可用性
            if hasattr(self, 'task_flow_controller') and self.task_flow_controller:
                # 使用TaskFlowController智能流转逻辑
                if self.task_flow_controller.debug_mode:
                    self.log_message(f"[TaskFlow] 使用TaskFlowController处理: {task_name}")

                return self.task_flow_controller.execute_task_flow(task, task_index)

            else:
                # TaskFlowController不可用，这是系统错误
                self.log_message(f"[ERROR] TaskFlowController不可用: {task_name}")
                self._update_task_progress(task_index, custom_status='系统错误')
                return False

        except InterruptedError:
            # 处理取消异常
            self.log_message(f"[CANCEL] 任务被取消: {task_name}")
            return False

        except Exception as e:
            # 统一异常处理
            if self.conversion_cancelled:
                self.log_message(f"[CANCEL] 任务在异常中被取消: {task_name}")
                return False

            self.log_message(f"[ERROR] 任务处理异常: {task_name} - {str(e)}")
            self._update_task_progress(task_index, custom_status='处理异常')

            # 更新TaskFlowController统计
            if hasattr(self, 'task_flow_controller') and self.task_flow_controller:
                self.task_flow_controller.stats['error_count'] += 1

            return False

    # _process_task方法已删除 - 功能已完全由TaskFlowController.execute_task_flow()替代
    # 所有任务处理现在统一通过TaskFlowController进行，确保EISA架构的纯洁性


    def _get_enabled_asr_services(self):
        """获取当前启用的ASR服务列表（不检查API Key，只检查开关状态）"""
        try:
            enabled_services = []

            # 检查ElevenLabs
            elevenlabs_enabled = self.asr_service_switches.get('elevenlabs_enabled', True)
            if elevenlabs_enabled:
                enabled_services.append('ElevenLabs')

            # 检查AssemblyAI
            assemblyai_enabled = self.asr_service_switches.get('assemblyai_enabled', True)
            if assemblyai_enabled:
                enabled_services.append('AssemblyAI')

            # 检查Deepgram
            deepgram_enabled = self.asr_service_switches.get('deepgram_enabled', True)
            if deepgram_enabled:
                enabled_services.append('Deepgram')

            return enabled_services

        except Exception as e:
            self.log_message(f"获取启用服务列表异常: {str(e)}")
            return []

    def _get_enabled_llm_apis(self):
        """获取当前启用的LLM API列表"""
        try:
            from services.llm.multi_api import get_multi_llm_service
            multi_llm_service = get_multi_llm_service()
            active_configs = multi_llm_service.llm_service.get_active_configs()
            return [config.name for config in active_configs]
        except Exception as e:
            self.log_message(f"获取LLM API列表失败: {str(e)}")
            return []

    def _extract_service_name_from_transcription_file(self, transcription_file):
        """从转录文件名提取服务名称"""
        # The Empty Sphere-ElevenLabs-parsed.json -> ElevenLabs
        parts = transcription_file.replace('-parsed.json', '').split('-')
        if len(parts) >= 2:
            return parts[-1]
        return "Unknown"

    def _validate_transcription_file(self, transcription_path):
        """验证转录文件的有效性"""
        try:
            with open(transcription_path, 'r', encoding='utf-8') as f:
                parsed_data = json.load(f)

            # 检查必要字段
            return (parsed_data.get('full_text', '').strip() and
                    parsed_data.get('words') and
                    len(parsed_data.get('words', [])) > 0)
        except Exception:
            return False

    def _find_merged_subtitle_files(self, task_folder, file_name):
        """查找现有的合并字幕文件"""
        try:
            merged_files = []

            # 构建合并文件的模式
            # 格式：{file_name}-{asr_service}-{llm_api}.srt
            import re
            pattern = re.compile(rf'{re.escape(file_name)}-([^-]+)-([^-]+)\.srt$')

            # 扫描项目目录
            for filename in os.listdir(task_folder):
                match = pattern.match(filename)
                if match:
                    file_path = os.path.join(task_folder, filename)
                    if os.path.exists(file_path) and os.path.getsize(file_path) > 0:
                        asr_service, llm_api = match.groups()
                        merged_files.append({
                            'filename': filename,
                            'path': file_path,
                            'asr_service': asr_service,
                            'llm_api': llm_api,
                            'size': os.path.getsize(file_path)
                        })

            return merged_files

        except Exception as e:
            self.log_message(f"[ERROR] 查找合并字幕文件异常: {e}")
            return []

    def _validate_merged_subtitle_file(self, file_path):
        """验证合并字幕文件的有效性"""
        try:
            if not os.path.exists(file_path) or os.path.getsize(file_path) == 0:
                return False

            # 基本验证：检查SRT格式
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 检查SRT格式
            import re
            entries = re.findall(r'^\d+$', content, re.MULTILINE)
            timecodes = re.findall(r'\d{2}:\d{2}:\d{2},\d{3}', content)

            return len(entries) > 0 and len(timecodes) >= 2

        except Exception as e:
            self.log_message(f"[ERROR] 验证合并字幕文件异常: {e}")
            return False

    def _get_required_asr_llm_combinations(self, task_folder, file_name):
        """根据当前配置生成所有需要的ASR-LLM组合

        生成逻辑：
        1. 获取启用的ASR服务列表
        2. 获取启用的LLM API列表
        3. 生成笛卡尔积组合
        4. 为每个组合构建文件路径信息

        Returns:
            list: ASR-LLM组合信息列表，包含服务名和文件路径
        """
        try:
            combinations = []

            # 1. 获取启用的ASR服务
            enabled_asr_services = self._get_enabled_asr_services()

            # 2. 获取启用的LLM API
            enabled_llm_apis = self._get_enabled_llm_apis()

            # 3. 生成所有需要的组合
            for asr_service in enabled_asr_services:
                for llm_api in enabled_llm_apis:
                    combination = {
                        'asr_service': asr_service,
                        'llm_api': llm_api,
                        'transcription_file': f"{file_name}-{asr_service}-parsed.json",
                        'transcription_path': os.path.join(task_folder, f"{file_name}-{asr_service}-parsed.json"),
                        'subtitle_file': f"{file_name}-{asr_service}-{llm_api}.srt",
                        'subtitle_path': os.path.join(task_folder, f"{file_name}-{asr_service}-{llm_api}.srt")
                    }
                    combinations.append(combination)

            return combinations

        except Exception as e:
            self.log_message(f"获取ASR-LLM组合失败: {str(e)}")
            return []

    def _analyze_asr_llm_combinations_status(self, task_folder, file_name):
        """分析ASR-LLM组合的处理状态

        使用三层分析策略：
        1. 策略1：基于合并文件分析（最可靠）
        2. 策略2：基于分段文件分析（降级策略，标记需要合并）
        3. 策略3：基础分析（兜底策略）

        Returns:
            dict: 包含组合状态信息的字典，失败返回None
        """
        try:
            # === 策略1：优先基于合并文件分析 ===
            merged_analysis = self._analyze_from_merged_files(task_folder, file_name)
            if merged_analysis['reliable']:
                return merged_analysis['status']

            # === 策略2：基于分段文件分析（降级策略） ===
            segment_analysis = self._analyze_from_segment_files(task_folder, file_name)
            if segment_analysis['reliable']:
                # 如果分段完整，需要标记为"需要合并"而不是"已完成"
                status = segment_analysis['status']
                if status.get('subtitle_complete', False):
                    status['subtitle_complete'] = False  # 重置完成标志
                    status['needs_merge'] = True  # 添加合并需求标志
                return status

            # === 策略3：基础分析（兜底策略） ===
            return self._analyze_basic_combinations_status(task_folder, file_name)

        except Exception as e:
            self.log_message(f"分析ASR-LLM组合状态失败: {str(e)}")
            return None

    def _analyze_from_merged_files(self, task_folder, file_name):
        """基于合并文件的状态分析"""
        try:
            enabled_asr_services = self._get_enabled_asr_services()
            enabled_llm_apis = self._get_enabled_llm_apis()

            total_combinations = len(enabled_asr_services) * len(enabled_llm_apis)
            if total_combinations == 0:
                return {'reliable': True, 'status': self._create_empty_status()}

            # 查找现有的合并文件
            merged_files = self._find_merged_subtitle_files(task_folder, file_name)

            # 验证合并文件的有效性
            valid_merged_files = []
            for merged_file in merged_files:
                if self._validate_merged_subtitle_file(merged_file['path']):
                    valid_merged_files.append(merged_file)

            subtitle_ready = len(valid_merged_files)

            return {
                'reliable': True,
                'status': {
                    'total_combinations': total_combinations,
                    'transcription_ready': total_combinations,  # 假设转录已完成
                    'subtitle_ready': subtitle_ready,
                    'transcription_complete': True,
                    'subtitle_complete': subtitle_ready == total_combinations,
                    'missing_transcriptions': [],
                    'missing_subtitles': self._identify_missing_merged_files(
                        enabled_asr_services, enabled_llm_apis, valid_merged_files
                    ),
                    'is_segmented': self._is_segmented_processing(task_folder, file_name),
                    'merged_files': valid_merged_files,
                    'analysis_method': 'merged_files'
                }
            }

        except Exception as e:
            self.log_message(f"基于合并文件的状态分析失败: {e}")
            return {'reliable': False}

    def _analyze_from_segment_files(self, task_folder, file_name):
        """基于分段文件的状态分析（降级策略）"""
        try:
            # 导入工具类
            from .segmentation_utils import SegmentationUtils

            # 检查是否为分段处理
            segment_files = SegmentationUtils.find_segment_files(task_folder, file_name)

            if segment_files:
                # 分段处理：使用工具类分析状态
                enabled_asr_services = self._get_enabled_asr_services()
                enabled_llm_apis = self._get_enabled_llm_apis()

                status = SegmentationUtils.analyze_segmented_combinations_status(
                    task_folder, segment_files, enabled_asr_services, enabled_llm_apis,
                    validate_transcription_func=self._validate_transcription_file
                )

                if status:
                    status['analysis_method'] = 'segment_files'
                    return {'reliable': True, 'status': status}

            return {'reliable': False}

        except Exception as e:
            self.log_message(f"基于分段文件的状态分析失败: {e}")
            return {'reliable': False}

    def _analyze_basic_combinations_status(self, task_folder, file_name):
        """基础组合状态分析（兜底策略）"""
        try:
            # 使用原有的单文件分析逻辑
            status = self._analyze_single_file_combinations_status(task_folder, file_name)
            if status:
                status['analysis_method'] = 'basic_single_file'
            return status

        except Exception as e:
            self.log_message(f"基础组合状态分析失败: {e}")
            return None

    def _create_empty_status(self):
        """创建空的状态对象"""
        return {
            'total_combinations': 0,
            'transcription_ready': 0,
            'subtitle_ready': 0,
            'transcription_complete': False,
            'subtitle_complete': False,
            'missing_transcriptions': [],
            'missing_subtitles': [],
            'is_segmented': False
        }

    def _identify_missing_merged_files(self, enabled_asr_services, enabled_llm_apis, valid_merged_files):
        """识别缺失的合并文件"""
        try:
            existing_combinations = set()
            for merged_file in valid_merged_files:
                existing_combinations.add((merged_file['asr_service'], merged_file['llm_api']))

            missing_combinations = []
            for asr_service in enabled_asr_services:
                for llm_api in enabled_llm_apis:
                    if (asr_service, llm_api) not in existing_combinations:
                        missing_combinations.append({
                            'asr_service': asr_service,
                            'llm_api': llm_api
                        })

            return missing_combinations

        except Exception as e:
            self.log_message(f"识别缺失合并文件异常: {e}")
            return []

    def _is_segmented_processing(self, task_folder, file_name):
        """检查是否为分段处理"""
        try:
            from .segmentation_utils import SegmentationUtils
            segment_files = SegmentationUtils.find_segment_files(task_folder, file_name)
            return len(segment_files) > 0
        except Exception:
            return False

    def _analyze_single_file_combinations_status(self, task_folder, file_name):
        """分析单文件的ASR-LLM组合状态（原有逻辑）"""
        try:
            required_combinations = self._get_required_asr_llm_combinations(task_folder, file_name)

            if not required_combinations:
                # 没有启用的服务组合 - 标记为不需要处理，而不是已完成
                return {
                    'total_combinations': 0,
                    'transcription_ready': 0,
                    'subtitle_ready': 0,
                    'transcription_complete': False,  # 修复：不需要处理不等于已完成
                    'subtitle_complete': False,       # 修复：不需要处理不等于已完成
                    'missing_transcriptions': [],
                    'missing_subtitles': [],
                    'is_segmented': False
                }

            transcription_ready = 0
            subtitle_ready = 0
            missing_transcriptions = []
            missing_subtitles = []

            for combination in required_combinations:
                # 检查转录文件
                if os.path.exists(combination['transcription_path']):
                    # 验证转录文件有效性
                    if self._validate_transcription_file(combination['transcription_path']):
                        transcription_ready += 1
                    else:
                        missing_transcriptions.append(combination)
                else:
                    missing_transcriptions.append(combination)

                # 检查字幕文件
                if os.path.exists(combination['subtitle_path']):
                    subtitle_ready += 1
                else:
                    missing_subtitles.append(combination)

            total_combinations = len(required_combinations)

            return {
                'total_combinations': total_combinations,
                'transcription_ready': transcription_ready,
                'subtitle_ready': subtitle_ready,
                'transcription_complete': transcription_ready == total_combinations,
                'subtitle_complete': subtitle_ready == total_combinations,
                'missing_transcriptions': missing_transcriptions,
                'missing_subtitles': missing_subtitles,
                'is_segmented': False
            }

        except Exception as e:
            self.log_message(f"分析单文件组合状态失败: {str(e)}")
            return None



    def _find_any_audio_file(self, task_folder, file_name):
        """查找已存在的音频文件（查找所有音频格式）"""
        try:
            # 常见的音频文件扩展名（按优先级排序）
            audio_extensions = ['.wav', '.flac', '.mp3', '.m4a', '.aac', '.ogg']

            for ext in audio_extensions:
                audio_file = os.path.join(task_folder, f"{file_name}{ext}")
                if os.path.exists(audio_file):
                    return audio_file

            return None

        except Exception as e:
            self.log_message(f"查找音频文件异常: {str(e)}")
            return None





    def _has_valid_transcriptions(self, task_folder, file_name):
        """检查是否有有效的转录文件（用于动态进度计算）"""
        try:
            # 查找所有转录文件
            transcription_files = []
            if os.path.exists(task_folder):
                for file in os.listdir(task_folder):
                    if file.startswith(file_name) and file.endswith('-parsed.json'):
                        file_path = os.path.join(task_folder, file)
                        # 检查文件大小和有效性
                        if os.path.getsize(file_path) > 100:  # 至少100字节
                            if self._validate_transcription_file(file_path):
                                transcription_files.append(file)

            return len(transcription_files) > 0

        except Exception as e:
            self.log_message(f"检查转录文件异常: {str(e)}")
            return False

    def _find_existing_transcriptions(self, task_folder, file_name):
        """检查是否有任何有效的转录文件（简化版本）

        这个方法被简化为只检查parsed.json文件的存在性，
        复杂的组合逻辑由EISA架构统一管理。
        """
        try:
            # 简单检查：是否有任何parsed.json文件
            for file in os.listdir(task_folder):
                if file.startswith(file_name) and file.endswith('-parsed.json'):
                    # 验证文件不为空
                    file_path = os.path.join(task_folder, file)
                    if os.path.getsize(file_path) > 0:
                        return True
            return False
        except Exception as e:
            self.log_message(f"检查转录结果异常: {str(e)}")
            return False

    def _check_service_transcription_status(self, task_folder, file_name, service):
        """
        检查单个服务的转录状态

        Returns:
            str: 'completed' | 'needs_parsing' | 'needs_transcription'
        """
        try:
            # 检查解析后的缓存文件
            parsed_file = os.path.join(task_folder, f"{file_name}-{service}-parsed.json")
            if os.path.exists(parsed_file):
                # 验证parsed文件的有效性
                try:
                    with open(parsed_file, 'r', encoding='utf-8') as f:
                        parsed_data = json.load(f)

                    # 检查必要字段
                    if (parsed_data.get('full_text', '').strip() and
                        parsed_data.get('words') and
                        len(parsed_data.get('words', [])) > 0):
                        return 'completed'  # 已完成且有效
                    else:
                        self.log_message(f"[WARN] {service} parsed文件无效，需要重新解析")
                        # 删除无效的parsed文件
                        os.remove(parsed_file)
                except (json.JSONDecodeError, KeyError) as e:
                    self.log_message(f"[WARN] {service} parsed文件损坏: {e}")
                    # 删除损坏的parsed文件
                    try:
                        os.remove(parsed_file)
                    except:
                        pass

            # 检查原始JSON文件
            json_file = os.path.join(task_folder, f"{file_name}-{service}.json")
            if os.path.exists(json_file):
                # 验证JSON文件的有效性
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        json_data = json.load(f)

                    # 检查基本结构
                    if (json_data.get('success') and
                        json_data.get('text', '').strip() and
                        json_data.get('words')):
                        return 'needs_parsing'  # 需要解析
                    else:
                        self.log_message(f"[WARN] {service} JSON文件无效，需要重新转录")
                        # 删除无效的JSON文件
                        os.remove(json_file)
                except (json.JSONDecodeError, KeyError) as e:
                    self.log_message(f"[WARN] {service} JSON文件损坏: {e}")
                    # 删除损坏的JSON文件
                    try:
                        os.remove(json_file)
                    except:
                        pass

            # 都没有或都无效，需要转录
            return 'needs_transcription'

        except Exception as e:
            self.log_message(f"检查服务 {service} 状态异常: {str(e)}")
            return 'needs_transcription'

    def _parse_service_json(self, task_folder, file_name, service):
        """
        解析单个服务的JSON文件并保存解析结果

        Args:
            task_folder: 任务文件夹路径
            file_name: 基础文件名
            service: 服务名称

        Returns:
            bool: 解析是否成功
        """
        try:
            from services.asr import TranscriptionParser

            json_file = os.path.join(task_folder, f"{file_name}-{service}.json")
            if not os.path.exists(json_file):
                self.log_message(f"[ERROR] {service} JSON文件不存在: {json_file}")
                return False

            self.log_message(f"开始解析 {service} 的JSON文件")

            # 创建解析器
            parser = TranscriptionParser()

            # 解析文件
            result = parser.parse_file(json_file, source_format='auto')

            if result and result.words:
                # 验证解析结果的有效性
                if not result.full_text.strip():
                    self.log_message(f"[ERROR] {service} 解析结果无有效文本内容")
                    return False

                if len(result.words) == 0:
                    self.log_message(f"[ERROR] {service} 解析结果无词汇信息")
                    return False

                # 构建解析结果数据
                parsed_data = {
                    'service': service.lower(),
                    'full_text': result.full_text,
                    'words': [
                        {
                            'word': word.word,
                            'start_time': word.start_time,
                            'end_time': word.end_time,
                            'confidence': word.confidence,
                            'speaker_id': word.speaker_id
                        }
                        for word in result.words
                    ],
                    'language': result.language,
                    'confidence': result.confidence,
                    'speaker_count': result.speaker_count,
                    'word_count': len(result.words),
                    'parsed_at': time.time()
                }

                # 保存解析结果
                parsed_file = os.path.join(task_folder, f"{file_name}-{service}-parsed.json")
                with open(parsed_file, 'w', encoding='utf-8') as f:
                    json.dump(parsed_data, f, ensure_ascii=False, indent=2)

                self.log_message(f"[OK] {service} JSON解析完成，已保存: {parsed_file}")
                self.log_message(f"  词汇数量: {len(result.words)}")
                self.log_message(f"  语言: {result.language or '未知'}")
                self.log_message(f"  置信度: {result.confidence:.2f}")

                return True
            else:
                self.log_message(f"[ERROR] {service} JSON解析失败: 无有效转录内容")
                return False

        except Exception as e:
            self.log_message(f"[ERROR] 解析 {service} JSON异常: {str(e)}")
            import traceback
            self.log_message(f"[DEBUG] 异常详情: {traceback.format_exc()}")
            return False

    def _update_task_progress(self, task_index, progress=None, custom_status=None, sync_with_file_state=None, update_buttons=True, force_progress=None):
        """更新任务进度和状态显示 - 线程安全的统一进度更新接口

        支持多种调用模式：
        - 自动模式：使用EISA智能计算进度和状态
        - 手动模式：指定具体进度值和状态文本
        - 强制模式：覆盖EISA计算结果

        Args:
            task_index: 任务索引
            progress: 手动指定的进度值
            custom_status: 自定义状态文本
            sync_with_file_state: 是否同步文件状态
            update_buttons: 是否更新按钮状态
            force_progress: 强制进度值
        """
        try:
            # 参数兼容性处理
            if 0 <= task_index < len(self.task_list):
                task = self.task_list[task_index]

                # 简化的参数处理逻辑
                if custom_status is not None:
                    # 有自定义状态：使用自定义状态和进度
                    actual_status = custom_status
                    actual_progress = force_progress if force_progress is not None else progress
                    use_eisa = False
                else:
                    # 没有自定义状态：使用EISA计算引擎
                    actual_status = None
                    actual_progress = progress
                    use_eisa = True

                # 更新任务数据
                if use_eisa and actual_status is None:
                    # 使用EISA计算引擎
                    progress_info = self.progress_calculator.calculate_progress(task)

                    # 构建显示文本
                    display_text = progress_info.stage.display_text
                    if progress_info.detail_text:
                        display_text = f"{display_text}: {progress_info.detail_text}"

                    self.task_list[task_index]['status'] = display_text
                    self.task_list[task_index]['progress'] = progress_info.progress_percent

                    # 存储EISA阶段信息用于智能按钮逻辑
                    self.task_list[task_index]['_combination_stage'] = progress_info.stage
                else:
                    # 使用自定义状态和进度
                    if actual_status is not None:
                        self.task_list[task_index]['status'] = actual_status
                    if actual_progress is not None:
                        self.task_list[task_index]['progress'] = actual_progress

                # UI更新（确保在正确的线程中）
                def update_ui():
                    self._update_task_list_view()
                    if update_buttons:
                        self.update_button_state(immediate=True)
                    self.page.update()

                # 检查当前线程并选择合适的更新方式
                try:
                    # 尝试直接更新（如果在主线程中）
                    if threading.current_thread() == threading.main_thread():
                        update_ui()
                    else:
                        # 在后台线程中，使用page.run_thread
                        self.page.run_thread(update_ui)
                except Exception:
                    # 如果线程检查失败，总是使用page.run_thread（更安全）
                    self.page.run_thread(update_ui)

        except Exception as e:
            self.log_message(f"更新任务进度异常: {e}")


    def _conversion_completed(self, completed, total):
        """转换完成后的处理"""

        def update_ui():
            # 重置转换状态标志
            self.conversion_cancelled = False
            self.conversion_thread = None

            # 使用统一的按钮状态更新逻辑
            self.update_button_state(immediate=True)

            # 使用统一接口的剩余任务统计
            remaining_tasks = sum(1 for task in self.task_list
                                if self.get_task_stage(task) != CombinationStage.COMPLETED)
            if remaining_tasks > 0:
                self.log_message(f"剩余 {remaining_tasks} 个任务可点击'继续'处理", is_debug=True)
            self._update_cancel_button_state()  # 使用新的阶段感知逻辑

            # 强制更新UI
            self.page.update()

            # 显示完成信息
            self.log_message(f"转换完成！成功: {completed}/{total}")
            if completed < total:
                self.log_message(f"失败: {total - completed} 个文件转换失败")

        self.page.run_thread(update_ui)


def main(page: ft.Page):
    """应用入口点"""
    # 设置窗口大小 - 保持合适的宽高比例
    page.title = "EvaTrans"
    page.window.width = 1200  # 增加宽度，保持比例
    page.window.height = 900
    page.window.min_width = 900
    page.window.min_height = 700
    page.window.resizable = True

    # 设置窗口居中显示
    page.window.center()

    # 设置应用图标 - 根据Flet官方文档
    # 注意：window.icon 功能仅在Windows桌面应用上有效
    try:
        if os.path.exists("assets/icon.ico"):
            page.window.icon = "assets/icon.ico"
            print(f"已设置窗口图标: assets/icon.ico")
            print(f"图标文件大小: {os.path.getsize('assets/icon.ico')} bytes")
        else:
            print("警告: 图标文件 assets/icon.ico 不存在")
    except Exception as e:
        print(f"设置窗口图标失败: {e}")

    print(f"当前工作目录: {os.getcwd()}")

    app = EvaTransMainApp(page)


if __name__ == "__main__":
    ft.app(target=main)
