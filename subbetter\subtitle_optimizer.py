#!/usr/bin/env python3
"""
字幕时间优化工具
使用FFmpeg的silencedetect滤镜检测静音，然后优化字幕时间戳，
剪掉字幕条目前后的静音区域，使字幕与语音精确对齐。

作者：EvaTrans项目
"""

import argparse
import re
import subprocess
import sys
from collections import namedtuple
from pathlib import Path
from typing import List, Optional, Tuple

# 数据结构定义
SilenceSegment = namedtuple('SilenceSegment', ['start', 'end'])
SubtitleEntry = namedtuple('SubtitleEntry', ['index', 'start_time', 'end_time', 'text'])


class SubtitleOptimizer:
    """字幕时间优化器"""
    
    def __init__(self, noise_threshold: float = -30.0, min_silence_duration: float = 0.01):
        """
        初始化优化器
        
        Args:
            noise_threshold: 静音阈值 (dB)，默认-30dB
            min_silence_duration: 最小静音持续时间 (秒)，默认0.01秒
        """
        self.noise_threshold = noise_threshold
        self.min_silence_duration = min_silence_duration
        
    def detect_silences(self, audio_file: str) -> List[SilenceSegment]:
        """
        使用FFmpeg检测音频中的静音段
        
        Args:
            audio_file: 音频文件路径
            
        Returns:
            静音段列表，按时间排序
        """
        print(f"正在检测音频静音段: {audio_file}")
        
        # 构建FFmpeg命令
        cmd = [
            'ffmpeg',
            '-i', audio_file,
            '-af', f'silencedetect=n={self.noise_threshold}dB:d={self.min_silence_duration}',
            '-f', 'null',
            '-'
        ]
        
        try:
            # 执行FFmpeg命令
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=False  # 不自动抛出异常，手动检查
            )
            
            if result.returncode != 0:
                print(f"FFmpeg执行警告 (返回码: {result.returncode})")
                print(f"错误信息: {result.stderr}")
                # 继续处理，因为FFmpeg在某些情况下会返回非0但仍然成功
            
            # 解析静音段信息
            silences = self._parse_silence_output(result.stderr)
            print(f"检测到 {len(silences)} 个静音段")
            
            return silences
            
        except FileNotFoundError:
            raise RuntimeError("未找到FFmpeg，请确保FFmpeg已安装并在PATH中")
        except Exception as e:
            raise RuntimeError(f"FFmpeg执行失败: {e}")
    
    def _parse_silence_output(self, stderr_output: str) -> List[SilenceSegment]:
        """
        解析FFmpeg silencedetect的输出
        
        Args:
            stderr_output: FFmpeg的stderr输出
            
        Returns:
            解析出的静音段列表
        """
        silences = []
        
        # 提取silence_start和silence_end
        start_pattern = r'silence_start:\s*(\d+\.?\d*)'
        end_pattern = r'silence_end:\s*(\d+\.?\d*)'
        
        start_matches = re.findall(start_pattern, stderr_output)
        end_matches = re.findall(end_pattern, stderr_output)
        
        # 处理开头和结尾的特殊情况
        start_times = [float(t) for t in start_matches]
        end_times = [float(t) for t in end_matches]
        
        # 如果开头就是静音，第一个end没有对应的start
        if len(end_times) > len(start_times):
            start_times.insert(0, 0.0)
        
        # 如果结尾是静音，最后一个start没有对应的end
        if len(start_times) > len(end_times):
            # 这种情况下，我们无法确定结尾时间，忽略最后一个start
            start_times = start_times[:-1]
        
        # 组合成静音段
        for start, end in zip(start_times, end_times):
            if end > start:  # 确保时间有效
                silences.append(SilenceSegment(start, end))
        
        # 按开始时间排序
        silences.sort(key=lambda s: s.start)
        
        return silences
    
    def parse_srt_file(self, srt_file: str) -> List[SubtitleEntry]:
        """
        解析SRT字幕文件
        
        Args:
            srt_file: SRT文件路径
            
        Returns:
            字幕条目列表
        """
        print(f"正在解析SRT文件: {srt_file}")
        
        try:
            with open(srt_file, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            # 尝试其他编码
            try:
                with open(srt_file, 'r', encoding='gbk') as f:
                    content = f.read()
            except UnicodeDecodeError:
                with open(srt_file, 'r', encoding='latin-1') as f:
                    content = f.read()
        
        entries = []
        blocks = content.strip().split('\n\n')
        
        for block in blocks:
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                try:
                    # 解析索引
                    index = int(lines[0])
                    
                    # 解析时间戳
                    time_line = lines[1]
                    start_str, end_str = time_line.split(' --> ')
                    start_time = self._srt_time_to_seconds(start_str)
                    end_time = self._srt_time_to_seconds(end_str)
                    
                    # 解析文本内容
                    text = '\n'.join(lines[2:])
                    
                    entries.append(SubtitleEntry(index, start_time, end_time, text))
                    
                except (ValueError, IndexError) as e:
                    print(f"警告: 跳过无效的字幕块: {block[:50]}... 错误: {e}")
                    continue
        
        print(f"解析到 {len(entries)} 个字幕条目")
        return entries
    
    def _srt_time_to_seconds(self, time_str: str) -> float:
        """
        将SRT时间格式转换为秒数
        
        Args:
            time_str: SRT时间格式 (HH:MM:SS,mmm)
            
        Returns:
            秒数 (浮点数)
        """
        # 格式: HH:MM:SS,mmm
        time_str = time_str.strip()
        time_part, ms_part = time_str.split(',')
        h, m, s = map(int, time_part.split(':'))
        ms = int(ms_part)
        
        return h * 3600 + m * 60 + s + ms / 1000.0
    
    def _seconds_to_srt_time(self, seconds: float) -> str:
        """
        将秒数转换为SRT时间格式

        Args:
            seconds: 秒数 (浮点数)

        Returns:
            SRT时间格式 (HH:MM:SS,mmm)
        """
        # 使用round避免浮点数精度问题
        total_ms = round(seconds * 1000)

        hours = total_ms // 3600000
        minutes = (total_ms % 3600000) // 60000
        secs = (total_ms % 60000) // 1000
        ms = total_ms % 1000

        return f"{hours:02d}:{minutes:02d}:{secs:02d},{ms:03d}"

    def optimize_subtitle_timing(self, silences: List[SilenceSegment],
                                entries: List[SubtitleEntry]) -> List[SubtitleEntry]:
        """
        优化字幕时间戳，剪掉前后的静音区域

        Args:
            silences: 静音段列表
            entries: 原始字幕条目列表

        Returns:
            优化后的字幕条目列表
        """
        print("正在优化字幕时间戳...")

        optimized_entries = []
        total_trimmed_time = 0.0

        for entry in entries:
            # 前向搜索：找到开始时间之后第一个语音开始点
            new_start = self._find_speech_start(entry.start_time, silences)

            # 后向搜索：找到结束时间之前最后一个语音结束点
            new_end = self._find_speech_end(entry.end_time, silences)

            # 确保新的时间戳有效
            if new_end <= new_start:
                # 如果优化后时间无效，保持原时间
                new_start = entry.start_time
                new_end = entry.end_time
                print(f"警告: 字幕 {entry.index} 优化后时间无效，保持原时间")
            else:
                # 计算节省的时间
                original_duration = entry.end_time - entry.start_time
                new_duration = new_end - new_start
                trimmed = original_duration - new_duration
                total_trimmed_time += trimmed

                if trimmed > 0.1:  # 只报告显著的修剪
                    print(f"字幕 {entry.index}: 修剪了 {trimmed:.3f}s "
                          f"({entry.start_time:.3f}-{entry.end_time:.3f} → "
                          f"{new_start:.3f}-{new_end:.3f})")

            # 创建优化后的条目
            optimized_entry = SubtitleEntry(
                entry.index, new_start, new_end, entry.text
            )
            optimized_entries.append(optimized_entry)

        print(f"优化完成，总共节省了 {total_trimmed_time:.3f} 秒")
        return optimized_entries

    def _find_speech_start(self, start_time: float, silences: List[SilenceSegment]) -> float:
        """
        从开始时间向内搜索，找到第一个语音开始点

        Args:
            start_time: 原始开始时间
            silences: 静音段列表

        Returns:
            优化后的开始时间
        """
        # 检查start_time是否在某个静音段中
        for silence in silences:
            if silence.start <= start_time <= silence.end:
                # 在静音中，返回静音结束点（语音开始点）
                return silence.end

        # 不在静音中，已经在语音中，保持原时间
        return start_time

    def _find_speech_end(self, end_time: float, silences: List[SilenceSegment]) -> float:
        """
        从结束时间向内搜索，找到最后一个语音结束点

        Args:
            end_time: 原始结束时间
            silences: 静音段列表

        Returns:
            优化后的结束时间
        """
        # 检查end_time是否在某个静音段中
        for silence in silences:
            if silence.start <= end_time <= silence.end:
                # 在静音中，返回静音开始点（语音结束点）
                return silence.start

        # 不在静音中，已经在语音中，保持原时间
        return end_time

    def save_optimized_srt(self, entries: List[SubtitleEntry], output_file: str):
        """
        保存优化后的SRT文件

        Args:
            entries: 优化后的字幕条目列表
            output_file: 输出文件路径
        """
        print(f"正在保存优化后的SRT文件: {output_file}")

        with open(output_file, 'w', encoding='utf-8') as f:
            for entry in entries:
                # 写入索引
                f.write(f"{entry.index}\n")

                # 写入时间戳
                start_str = self._seconds_to_srt_time(entry.start_time)
                end_str = self._seconds_to_srt_time(entry.end_time)
                f.write(f"{start_str} --> {end_str}\n")

                # 写入文本内容
                f.write(f"{entry.text}\n\n")

        print("SRT文件保存完成")

    def expand_subtitles(self, subtitles: List[SubtitleEntry], expand_ms: int = 200) -> List[SubtitleEntry]:
        """
        扩充字幕显示时间

        Args:
            subtitles: 字幕条目列表
            expand_ms: 扩充时间（毫秒），开始时间提前，结束时间延后

        Returns:
            扩充后的字幕条目列表
        """
        if expand_ms <= 0:
            return subtitles

        expand_seconds = expand_ms / 1000.0

        # 统计变量
        original_count = len(subtitles)
        fully_expanded = 0
        partially_expanded = 0
        total_time_added = 0.0

        # 记录原始时长
        original_durations = [(sub.end_time - sub.start_time) for sub in subtitles]

        # 创建新的字幕列表（避免修改原列表）
        expanded_subtitles = []

        # 第一步：扩充所有字幕
        for subtitle in subtitles:
            new_start = max(0, subtitle.start_time - expand_seconds)  # 不能小于0
            new_end = subtitle.end_time + expand_seconds
            expanded_subtitles.append(SubtitleEntry(subtitle.index, new_start, new_end, subtitle.text))

        # 第二步：解决重叠冲突
        conflicts_resolved = 0
        for i in range(len(expanded_subtitles) - 1):
            current = expanded_subtitles[i]
            next_subtitle = expanded_subtitles[i + 1]

            if current.end_time >= next_subtitle.start_time:  # 检测重叠（包括相同时间戳）
                conflicts_resolved += 1
                # 在重叠区域的中点分割，并添加1ms间隔避免相同时间戳
                midpoint = (current.end_time + next_subtitle.start_time) / 2
                gap = 0.001  # 1ms间隔

                # 调整两个字幕的边界，确保有间隔
                expanded_subtitles[i] = SubtitleEntry(current.index, current.start_time, midpoint - gap/2, current.text)
                expanded_subtitles[i+1] = SubtitleEntry(next_subtitle.index, midpoint + gap/2, next_subtitle.end_time, next_subtitle.text)

        # 计算统计信息
        for i, (original_duration, expanded) in enumerate(zip(original_durations, expanded_subtitles)):
            new_duration = expanded.end_time - expanded.start_time
            time_added = new_duration - original_duration
            total_time_added += time_added

            if abs(time_added - 2 * expand_seconds) < 0.001:
                fully_expanded += 1
            elif time_added > 0.001:
                partially_expanded += 1

        # 输出统计信息
        success_rate = (fully_expanded / original_count) * 100 if original_count > 0 else 0
        print(f"扩充统计:")
        print(f"  处理字幕: {original_count} 个")
        print(f"  完全扩充: {fully_expanded} 个 ({success_rate:.1f}%)")
        print(f"  部分扩充: {partially_expanded} 个")
        print(f"  无法扩充: {original_count - fully_expanded - partially_expanded} 个")
        if conflicts_resolved > 0:
            print(f"  解决冲突: {conflicts_resolved} 个")
        print(f"  总增加时间: {total_time_added:.3f} 秒")

        return expanded_subtitles

    def bridge_gaps(self, subtitles: List[SubtitleEntry], bridge_ms: int = 300) -> List[SubtitleEntry]:
        """
        桥接字幕间的短间隔

        Args:
            subtitles: 字幕条目列表
            bridge_ms: 桥接阈值（毫秒），小于此值的间隔将被桥接

        Returns:
            桥接后的字幕条目列表
        """
        if bridge_ms <= 0:
            return subtitles

        bridge_seconds = bridge_ms / 1000.0
        bridged_count = 0
        total_time_bridged = 0.0

        # 创建新列表避免修改原列表
        bridged_subtitles = []

        for i, subtitle in enumerate(subtitles):
            if i == len(subtitles) - 1:
                # 最后一个字幕，直接添加
                bridged_subtitles.append(subtitle)
            else:
                current = subtitle
                next_subtitle = subtitles[i + 1]

                # 计算间隔
                gap = next_subtitle.start_time - current.end_time

                if 0.002 < gap <= bridge_seconds:  # 2ms以下不桥接
                    # 需要桥接：在间隔中点分割
                    midpoint = (current.end_time + next_subtitle.start_time) / 2

                    # 创建桥接后的当前字幕
                    bridged_current = SubtitleEntry(
                        current.index,
                        current.start_time,
                        midpoint,
                        current.text
                    )
                    bridged_subtitles.append(bridged_current)

                    # 修改下一个字幕的开始时间（在下次循环中处理）
                    subtitles[i + 1] = SubtitleEntry(
                        next_subtitle.index,
                        midpoint,
                        next_subtitle.end_time,
                        next_subtitle.text
                    )

                    # 统计信息
                    bridged_count += 1
                    total_time_bridged += gap

                else:
                    # 不需要桥接，直接添加
                    bridged_subtitles.append(current)

        # 输出统计信息
        print(f"桥接统计:")
        print(f"  处理间隔: {bridged_count} 个")
        print(f"  桥接阈值: {bridge_ms}ms")
        print(f"  消除间隔: {total_time_bridged:.3f} 秒")

        return bridged_subtitles

    def process(self, audio_file: str, srt_file: str, output_file: str, expand_ms: int = 200, bridge_ms: int = 300):
        """
        完整的处理流程：优化 + 扩充

        Args:
            audio_file: 音频文件路径
            srt_file: 原始SRT文件路径
            output_file: 输出SRT文件路径
            expand_ms: 扩充时间（毫秒），默认200ms
        """
        try:
            # 1. 检测静音段
            silences = self.detect_silences(audio_file)

            # 2. 解析SRT文件
            entries = self.parse_srt_file(srt_file)

            # 3. 优化时间戳
            optimized_entries = self.optimize_subtitle_timing(silences, entries)

            # 4. 扩充处理
            if expand_ms > 0:
                print(f"\n正在扩充字幕显示时间 ({expand_ms}ms)...")
                expanded_entries = self.expand_subtitles(optimized_entries, expand_ms)
                print("扩充完成")
            else:
                print("\n跳过扩充处理")
                expanded_entries = optimized_entries

            # 5. 桥接处理
            if bridge_ms > 0:
                print(f"\n正在桥接短间隔 ({bridge_ms}ms)...")
                final_entries = self.bridge_gaps(expanded_entries, bridge_ms)
                print("桥接完成")
            else:
                print("\n跳过桥接处理")
                final_entries = expanded_entries

            # 6. 保存结果
            self.save_optimized_srt(final_entries, output_file)

            print("处理完成！")

        except Exception as e:
            print(f"处理失败: {e}")
            sys.exit(1)


def main():
    """主函数，处理命令行参数"""
    parser = argparse.ArgumentParser(
        description='字幕时间优化工具 - 使用FFmpeg silencedetect剪掉字幕前后的静音区域',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python subtitle_optimizer.py audio.wav subtitles.srt output.srt
  python subtitle_optimizer.py audio.mp3 subtitles.srt output.srt -n -35 -d 0.005

注意事项:
  - 需要安装FFmpeg并确保在PATH中
  - 支持常见的音频格式 (wav, mp3, m4a, flac等)
  - 输入和输出SRT文件使用UTF-8编码
        """
    )

    # 必需参数
    parser.add_argument('audio_file', help='音频文件路径')
    parser.add_argument('srt_file', help='输入SRT字幕文件路径')
    parser.add_argument('output_file', help='输出SRT字幕文件路径')

    # 可选参数
    parser.add_argument('-n', '--noise-threshold', type=float, default=-30.0,
                       help='静音阈值 (dB)，默认-30dB。更小的值检测更严格')
    parser.add_argument('-d', '--min-duration', type=float, default=0.01,
                       help='最小静音持续时间 (秒)，默认0.01秒')
    parser.add_argument('-e', '--expand', type=int, default=200,
                       help='扩充字幕显示时间 (毫秒)，默认200ms。设为0禁用扩充')
    parser.add_argument('-b', '--bridge', type=int, default=300,
                       help='桥接短间隔阈值 (毫秒)，默认300ms。设为0禁用桥接')
    parser.add_argument('--version', action='version', version='字幕优化器 v1.0')

    args = parser.parse_args()

    # 验证输入文件
    audio_path = Path(args.audio_file)
    srt_path = Path(args.srt_file)

    if not audio_path.exists():
        print(f"错误: 音频文件不存在: {args.audio_file}")
        sys.exit(1)

    if not srt_path.exists():
        print(f"错误: SRT文件不存在: {args.srt_file}")
        sys.exit(1)

    # 检查输出目录
    output_path = Path(args.output_file)
    output_path.parent.mkdir(parents=True, exist_ok=True)

    # 显示处理信息
    print("=" * 60)
    print("字幕时间优化工具")
    print("=" * 60)
    print(f"音频文件: {args.audio_file}")
    print(f"字幕文件: {args.srt_file}")
    print(f"输出文件: {args.output_file}")
    print(f"静音阈值: {args.noise_threshold}dB")
    print(f"最小静音时长: {args.min_duration}秒")
    print(f"扩充时间: {args.expand}ms")
    print(f"桥接阈值: {args.bridge}ms")
    print("=" * 60)

    # 创建优化器并处理
    optimizer = SubtitleOptimizer(
        noise_threshold=args.noise_threshold,
        min_silence_duration=args.min_duration
    )

    optimizer.process(args.audio_file, args.srt_file, args.output_file, args.expand, args.bridge)


if __name__ == '__main__':
    main()
