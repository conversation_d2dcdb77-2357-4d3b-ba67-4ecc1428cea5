#!/usr/bin/env python3
"""
配置管理模块 - 负责应用配置的加载、保存和管理
"""

import os
import json
from typing import Dict, Any, List, Tuple

# 配置常量（硬编码）
CONFIG_DIR = os.path.join(os.path.expanduser("~"), ".evatrans_gui")
CONFIG_FILE = os.path.join(CONFIG_DIR, "config.json")




class ConfigManager:
    """配置管理器 - 统一管理应用配置的加载、保存和验证

    核心功能：
    - 配置文件读写
    - ASR服务配置管理
    - LLM API配置管理
    - 配置结构化输出
    """

    def __init__(self, app_instance):
        """初始化配置管理器，加载默认配置和用户配置"""
        self.config = {}

        self.llm_settings = {}
        self.elevenlabs_settings = {}
        self.assemblyai_settings = {}
        self.deepgram_settings = {}
        self.asr_service_switches = {}

        # 初始化结构化配置写入器
        self._init_structured_config_writer()

        self._init_config()
        self._init_llm_settings()

    def _get_default_config(self):
        """获取默认配置结构"""
        return {
            "project_path": "./projects",
            "subtitle_path": "./subtitles",
            "user_llm_system_prompt": "",
            "asr_service_configs": [
                {
                    "id": "elevenlabs_config",
                    "service_name": "elevenlabs",
                    "api_key": "",
                    "enabled": False,
                    "language": "auto",
                    "enable_diarization": True,
                    "mode": "free",
                    "model_id": "scribe_v1",
                    "tag_audio_events": True,
                    "num_speakers": 0
                },
                {
                    "id": "assemblyai_config",
                    "service_name": "assemblyai",
                    "api_key": "",
                    "enabled": False,
                    "speech_model": "universal",
                    "language_code": "auto",
                    "language_detection": True,
                    "speaker_labels": True,
                    "speakers_expected": 0,
                    "punctuate": True,
                    "format_text": True,
                    "disfluencies": False,
                    "sentiment_analysis": False,
                    "entity_detection": False,
                    "auto_highlights": False,
                    "auto_chapters": False,
                    "summarization": False
                },
                {
                    "id": "deepgram_config",
                    "service_name": "deepgram",
                    "api_key": "",
                    "enabled": False,
                    "language": "auto",
                    "model": "nova-3",
                    "enable_diarization": True,
                    "enable_punctuation": True,
                    "utterances": True,
                    "smart_format": True,
                    "paragraphs": True,
                    "detect_language": True
                }
            ],
            "llm_api_configs": [
                {
                    "id": "config_1",
                    "name": "API1",
                    "base_url": "https://tbai.xin",
                    "model": "gemini-2.5-pro",
                    "api_key": "",
                    "format_type": "openai",
                    "enabled": True,
                    "timeout": 180,
                    "retry_count": 3,
                    "temperature": 0.01,
                    "request_interval": 10.0,
                    "available_models": [
                        "bge-reranker-v2-m3", "deepseek-r1", "deepseek-v3", "gemini-2.5-flash",
                        "gemini-2.5-flash-lite", "gemini-2.5-flash-nothinking", "gemini-2.5-flash-search",
                        "gemini-2.5-pro", "gemini-2.5-pro-preview-05-06", "gemini-2.5-pro-preview-06-05",
                        "gemini-2.5-pro-search", "gpt-4.1-mini", "gpt-4.1-nano", "gpt-4o-mini",
                        "qwen3-embedding-8b", "qwen3-reranker-8b"
                    ]
                },
                {
                    "id": "config_2",
                    "name": "API2",
                    "base_url": "https://api.voct.dev",
                    "model": "gemini-2.5-flash",
                    "api_key": "",
                    "format_type": "openai",
                    "enabled": True,
                    "timeout": 180,
                    "retry_count": 3,
                    "temperature": 0.01,
                    "request_interval": 10.0,
                    "available_models": [
                        "gemini-2.5-flash", "gemini-2.5-flash-preview-05-20",
                        "gemini-2.5-pro", "gemini-2.5-pro-preview-05-06"
                    ]
                },
                {
                    "id": "config_3",
                    "name": "API3",
                    "base_url": "https://api-proxy.me/gemini",
                    "model": "gemini-2.5-pro",
                    "api_key": "",
                    "format_type": "gemini",
                    "enabled": True,
                    "timeout": 180,
                    "retry_count": 3,
                    "temperature": 0.02,
                    "request_interval": 10.0,
                    "available_models": [
                        "aqa", "embedding-001", "embedding-gecko-001", "gemini-1.0-pro-vision-latest",
                        "gemini-1.5-flash", "gemini-1.5-flash-002", "gemini-1.5-flash-8b", "gemini-1.5-flash-8b-001",
                        "gemini-1.5-flash-8b-latest", "gemini-1.5-flash-latest", "gemini-1.5-pro", "gemini-1.5-pro-002",
                        "gemini-1.5-pro-latest", "gemini-2.0-flash", "gemini-2.0-flash-001", "gemini-2.0-flash-exp",
                        "gemini-2.0-flash-exp-image-generation", "gemini-2.0-flash-lite", "gemini-2.0-flash-lite-001",
                        "gemini-2.0-flash-lite-preview", "gemini-2.0-flash-lite-preview-02-05", "gemini-2.0-flash-preview-image-generation",
                        "gemini-2.0-flash-thinking-exp", "gemini-2.0-flash-thinking-exp-01-21", "gemini-2.0-flash-thinking-exp-1219",
                        "gemini-2.0-pro-exp", "gemini-2.0-pro-exp-02-05", "gemini-2.5-flash", "gemini-2.5-flash-lite-preview-06-17",
                        "gemini-2.5-flash-preview-04-17", "gemini-2.5-flash-preview-04-17-thinking", "gemini-2.5-flash-preview-05-20",
                        "gemini-2.5-flash-preview-tts", "gemini-2.5-pro", "gemini-2.5-pro-preview-03-25", "gemini-2.5-pro-preview-05-06",
                        "gemini-2.5-pro-preview-06-05", "gemini-2.5-pro-preview-tts", "gemini-embedding-exp", "gemini-embedding-exp-03-07",
                        "gemini-exp-1206", "gemini-pro-vision", "gemma-3-12b-it", "gemma-3-1b-it", "gemma-3-27b-it",
                        "gemma-3-4b-it", "gemma-3n-e2b-it", "gemma-3n-e4b-it", "learnlm-2.0-flash-experimental", "text-embedding-004"
                    ]
                }
            ],
            "show_debug_logs": False
        }

    def _init_config(self):
        """初始化配置文件，确保配置目录存在并加载用户配置"""
        # 确保配置目录存在
        os.makedirs(CONFIG_DIR, exist_ok=True)

        try:
            if os.path.exists(CONFIG_FILE):
                with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                self.config = self._get_default_config()
                self.write_config(self.config, CONFIG_FILE)
        except Exception as e:
            self.config = self._get_default_config()

    def _init_llm_settings(self):
        """初始化LLM相关设置，包括系统提示词和ASR服务配置"""
        self.llm_settings = {
            "user_llm_system_prompt": self.config.get("user_llm_system_prompt", ""),
        }

        # 从嵌套结构初始化ASR服务设置
        self._init_asr_settings()

    def _init_asr_settings(self):
        """从嵌套配置初始化ASR服务设置"""
        asr_configs = self.config.get('asr_service_configs', [])

        # 初始化各服务设置为默认值
        self.elevenlabs_settings = {}
        self.assemblyai_settings = {}
        self.deepgram_settings = {}
        self.asr_service_switches = {}

        # 如果没有嵌套配置，使用默认值
        if not asr_configs:
            default_config = self._get_default_config()
            asr_configs = default_config.get('asr_service_configs', [])

        # 处理每个ASR服务配置
        for asr_config in asr_configs:
            service_name = asr_config.get('service_name', '')

            if service_name == 'elevenlabs':
                self.elevenlabs_settings.update({
                    'language': asr_config.get('language', 'auto'),
                    'num_speakers': asr_config.get('num_speakers', 0),
                    'tag_audio_events': asr_config.get('tag_audio_events', True),
                    'model_id': asr_config.get('model_id', 'scribe_v1'),
                    'diarize': asr_config.get('enable_diarization', True),
                    # timestamps_granularity已在ASR服务层面固定
                    'mode': asr_config.get('mode', 'free'),
                    'api_key': asr_config.get('api_key', '')
                })
                self.asr_service_switches['elevenlabs_enabled'] = asr_config.get('enabled', False)

            elif service_name == 'assemblyai':
                self.assemblyai_settings.update({
                    'api_key': asr_config.get('api_key', ''),
                    'speech_model': asr_config.get('speech_model', 'universal'),
                    'language_code': asr_config.get('language_code', 'auto'),
                    'language_detection': asr_config.get('language_detection', True),
                    'speaker_labels': asr_config.get('speaker_labels', True),
                    'speakers_expected': asr_config.get('speakers_expected', 0),
                    'punctuate': asr_config.get('punctuate', True),
                    'format_text': asr_config.get('format_text', True),
                    'disfluencies': asr_config.get('disfluencies', False),
                    'sentiment_analysis': asr_config.get('sentiment_analysis', False),
                    'entity_detection': asr_config.get('entity_detection', False),
                    'auto_highlights': asr_config.get('auto_highlights', False),
                    'auto_chapters': asr_config.get('auto_chapters', False),
                    'summarization': asr_config.get('summarization', False)
                })
                self.asr_service_switches['assemblyai_enabled'] = asr_config.get('enabled', False)

            elif service_name == 'deepgram':
                self.deepgram_settings.update({
                    'api_key': asr_config.get('api_key', ''),
                    'model': asr_config.get('model', 'nova-3'),
                    'language': asr_config.get('language', 'auto')
                })
                self.asr_service_switches['deepgram_enabled'] = asr_config.get('enabled', False)
    
    def save_config(self):
        """保存配置到文件"""
        try:
            if not os.path.exists(CONFIG_DIR):
                os.makedirs(CONFIG_DIR)

            self.write_config(self.config, CONFIG_FILE)
        except Exception as e:
            pass

    def save_asr_service_settings(self):
        """保存ASR服务设置到配置文件

        将UI设置同步到JSON配置文件，ASR服务层直接读取JSON配置
        """
        # 确保asr_service_configs数组存在
        if 'asr_service_configs' not in self.config:
            self.config['asr_service_configs'] = []

        asr_configs = self.config['asr_service_configs']

        # 从UI设置同步到JSON配置
        for config in asr_configs:
            service_name = config.get('service_name', '')

            if service_name == 'elevenlabs':
                # 更新ElevenLabs配置
                config.update({
                    'api_key': self.elevenlabs_settings.get('api_key', ''),
                    'enabled': self.asr_service_switches.get('elevenlabs_enabled', False),
                    'language': self.elevenlabs_settings.get('language', 'auto'),
                    'num_speakers': self.elevenlabs_settings.get('num_speakers', 0),
                    'tag_audio_events': self.elevenlabs_settings.get('tag_audio_events', False),
                    'model_id': self.elevenlabs_settings.get('model_id', 'scribe_v1'),
                    'enable_diarization': self.elevenlabs_settings.get('diarize', True),
                    # timestamps_granularity已在ASR服务层面固定
                    'mode': self.elevenlabs_settings.get('mode', 'free')
                })

            elif service_name == 'assemblyai':
                # 更新AssemblyAI配置
                config.update({
                    'api_key': self.assemblyai_settings.get('api_key', ''),
                    'enabled': self.asr_service_switches.get('assemblyai_enabled', False),
                    'speech_model': self.assemblyai_settings.get('speech_model', 'universal'),
                    'language_code': self.assemblyai_settings.get('language_code', 'auto'),
                    'language_detection': self.assemblyai_settings.get('language_detection', True),
                    'speaker_labels': self.assemblyai_settings.get('speaker_labels', True),
                    'speakers_expected': self.assemblyai_settings.get('speakers_expected', 0),
                    'punctuate': self.assemblyai_settings.get('punctuate', True),
                    'format_text': self.assemblyai_settings.get('format_text', True),
                    'disfluencies': self.assemblyai_settings.get('disfluencies', False),
                    'sentiment_analysis': self.assemblyai_settings.get('sentiment_analysis', False),
                    'entity_detection': self.assemblyai_settings.get('entity_detection', False),
                    'auto_highlights': self.assemblyai_settings.get('auto_highlights', False),
                    'auto_chapters': self.assemblyai_settings.get('auto_chapters', False),
                    'summarization': self.assemblyai_settings.get('summarization', False)
                })

            elif service_name == 'deepgram':
                # 更新Deepgram配置
                config.update({
                    'api_key': self.deepgram_settings.get('api_key', ''),
                    'enabled': self.asr_service_switches.get('deepgram_enabled', False),
                    'model': self.deepgram_settings.get('model', 'nova-3'),
                    'language': self.deepgram_settings.get('language', 'auto')
                })

        self.save_config()

    # 保留旧方法名以兼容现有代码，但内部调用新方法
    def save_deepgram_settings(self):
        """保存Deepgram设置到配置（兼容性方法）"""
        self.save_asr_service_settings()

    def save_asr_switch_settings(self):
        """保存ASR服务开关设置到配置（兼容性方法）"""
        self.save_asr_service_settings()

    # ========== 直接ASR配置访问方法 ==========

    def get_asr_service_config(self, service_name: str) -> dict:
        """
        直接获取ASR服务配置，供服务层使用

        Args:
            service_name: 服务名称 (elevenlabs/assemblyai/deepgram)

        Returns:
            dict: ASR服务配置字典，包含所有必要的配置参数
        """
        asr_configs = self.config.get('asr_service_configs', [])

        # 查找对应服务的配置
        for config in asr_configs:
            if config.get('service_name') == service_name:
                # 返回配置副本，避免意外修改
                result = config.copy()

                # 确保包含最新的enabled状态（从asr_service_switches同步）
                enabled_key = f'{service_name}_enabled'
                result['enabled'] = self.asr_service_switches.get(enabled_key, False)

                # 清理UI专用字段
                return self._clean_asr_config_for_service(result)

        # 如果没找到，返回默认配置
        return self._get_default_asr_config(service_name)

    def _clean_asr_config_for_service(self, config: dict) -> dict:
        """清理ASR配置，移除UI专用字段

        Args:
            config: 原始配置字典

        Returns:
            dict: 清理后的配置字典，只包含ASR服务需要的字段
        """
        result = config.copy()

        # UI专用字段列表（集中管理，易于扩展）
        ui_only_fields = [
            'id',           # UI配置识别用
            # 'enabled',    # 暂时保留，某些服务可能需要
            # 未来可能的UI专用字段可以在这里添加
        ]

        # 移除UI专用字段
        for field in ui_only_fields:
            result.pop(field, None)

        return result

    def _get_default_asr_config(self, service_name: str) -> dict:
        """
        获取默认ASR配置

        Args:
            service_name: 服务名称

        Returns:
            dict: 默认配置
        """
        default_configs = self._get_default_config()
        asr_configs = default_configs.get('asr_service_configs', [])

        for config in asr_configs:
            if config.get('service_name') == service_name:
                result = config.copy()
                # 设置默认的enabled状态
                result['enabled'] = False
                # 清理UI专用字段
                return self._clean_asr_config_for_service(result)

        # 如果连默认配置都没有，返回基础配置
        return {
            'service_name': service_name,
            'api_key': '',
            'enabled': False,
            'language': 'auto',
            'timeout': 300,
            'max_retries': 3
        }



    def get_llm_system_prompt(self) -> str:
        """获取LLM系统提示词"""
        return self.config.get("user_llm_system_prompt", "")

    def set_llm_system_prompt(self, prompt: str) -> None:
        """设置LLM系统提示词"""
        self.config["user_llm_system_prompt"] = prompt
        self.llm_settings["user_llm_system_prompt"] = prompt

    # ========== 结构化配置写入器方法 ==========

    def _sync_llm_configs(self, config_dict: Dict[str, Any]) -> None:
        """同步LLM服务的最新配置到config_dict，防止覆盖"""
        try:
            from services.llm import get_multi_llm_service
            from dataclasses import asdict

            llm_service = get_multi_llm_service()

            # 如果LLM服务没有配置，保持config_dict中的配置
            if not llm_service.configs and 'llm_api_configs' in config_dict:
                # 记录调试信息但不修改配置
                return

            # 只有在LLM服务有配置时才同步
            if llm_service.configs:
                llm_configs_data = []
                for config in llm_service.configs:
                    config_dict_data = asdict(config)
                    # 转换enum为字符串
                    config_dict_data['format_type'] = config_dict_data['format_type'].value
                    llm_configs_data.append(config_dict_data)

                # 更新config_dict中的llm_api_configs
                config_dict['llm_api_configs'] = llm_configs_data

        except Exception as e:
            # 如果同步失败，记录错误但不影响正常写入流程
            pass

    def _init_structured_config_writer(self):
        """初始化结构化配置写入器"""
        self.sections = [
            ('基础应用配置', ['project_path', 'subtitle_path']),
            ('ASR服务配置', ['asr_service_configs']),
            ('LLM服务全局配置', [
                'user_llm_system_prompt'
            ]),
            ('LLM API配置列表', ['llm_api_configs']),
            ('调试配置', ['show_debug_logs'])
        ]

        # 定义配置项白名单
        self.valid_config_keys = set()
        for section_name, keys in self.sections:
            self.valid_config_keys.update(keys)

        # 定义参数注释
        self.comments = {
            'project_path': '项目工作目录',
            'subtitle_path': '字幕输出目录',
            'asr_service_configs': 'ASR服务配置列表',
            'user_llm_system_prompt': 'LLM系统提示词',
            'llm_api_configs': 'LLM API配置列表',
            'show_debug_logs': '显示调试日志开关'
        }

    def write_config(self, config_dict: Dict[str, Any], file_path: str) -> None:
        """写入结构化配置"""
        # 在写入前同步LLM服务的最新配置，防止覆盖
        self._sync_llm_configs(config_dict)

        lines = ['{']

        processed_keys = set()

        # 按sections顺序写入配置项
        for i, (section_name, keys) in enumerate(self.sections):
            if i > 0:
                lines.append('')

            section_has_content = False
            for key in keys:
                if key in config_dict:
                    if key == 'asr_service_configs':
                        asr_config_lines = self._write_asr_service_configs(config_dict, 1)
                        lines.extend(asr_config_lines)
                        processed_keys.add(key)
                        section_has_content = True
                    elif key == 'llm_api_configs':
                        api_config_lines = self._write_llm_api_configs(config_dict, 1)
                        lines.extend(api_config_lines)
                        processed_keys.add(key)
                        section_has_content = True
                    else:
                        value = self._format_value(config_dict[key])
                        lines.append(f'  "{key}": {value},')
                        processed_keys.add(key)
                        section_has_content = True

            if not section_has_content and lines[-1] == '':
                lines.pop()

        # 写入剩余的有效配置项
        remaining_keys = set(config_dict.keys()) - processed_keys
        valid_remaining_keys = remaining_keys & self.valid_config_keys

        if valid_remaining_keys:
            if lines[-1] != '':  # 如果最后一行不是空行，添加空行
                lines.append('')

            for key in sorted(valid_remaining_keys):  # 排序保证输出稳定
                if key not in ['llm_api_configs', 'asr_service_configs']:  # 避免重复处理复杂结构
                    value = self._format_value(config_dict[key])
                    lines.append(f'  "{key}": {value},')

        # 移除最后一个逗号
        if lines[-1].endswith(','):
            lines[-1] = lines[-1][:-1]

        lines.append('}')

        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))

    def _write_llm_api_configs(self, config_dict: Dict[str, Any], indent_level: int) -> List[str]:
        """构建llm_api_configs的JSON字符串"""
        base_indent = '  ' * indent_level
        item_indent = '  ' * (indent_level + 1)
        field_indent = '  ' * (indent_level + 2)

        lines = [f'{base_indent}"llm_api_configs": [']

        api_configs = config_dict.get('llm_api_configs', [])
        for i, config in enumerate(api_configs):
            lines.append(f'{item_indent}{{')


            lines.append(f'{field_indent}"id": "{config.get("id", "")}",')
            lines.append(f'{field_indent}"name": "{config.get("name", "")}",')
            lines.append(f'{field_indent}"base_url": "{config.get("base_url", "")}",')
            lines.append(f'{field_indent}"model": "{config.get("model", "")}",')
            lines.append(f'{field_indent}"api_key": "{config.get("api_key", "")}",')
            lines.append(f'{field_indent}"format_type": "{config.get("format_type", "")}",')
            lines.append(f'{field_indent}"enabled": {str(config.get("enabled", False)).lower()},')
            lines.append(f'{field_indent}"timeout": {config.get("timeout", 180)},')
            lines.append(f'{field_indent}"retry_count": {config.get("retry_count", 3)},')
            lines.append(f'{field_indent}"temperature": {config.get("temperature", 0.01)},')
            lines.append(f'{field_indent}"request_interval": {config.get("request_interval", 10.0)},')


            models = config.get("models", [])
            lines.append(f'{field_indent}"models": [')
            for j, model in enumerate(models):
                comma = ',' if j < len(models) - 1 else ''
                lines.append(f'{field_indent}  "{model}"{comma}')
            lines.append(f'{field_indent}]')


            comma = ',' if i < len(api_configs) - 1 else ''
            lines.append(f'{item_indent}}}{comma}')


        is_last = self._is_llm_api_configs_last_field(config_dict)
        comma = '' if is_last else ','
        lines.append(f'{base_indent}]{comma}')

        return lines

    def _write_asr_service_configs(self, config_dict: Dict[str, Any], indent_level: int) -> List[str]:
        """Build asr_service_configs JSON with proper formatting"""
        base_indent = '  ' * indent_level
        item_indent = '  ' * (indent_level + 1)
        field_indent = '  ' * (indent_level + 2)

        lines = [f'{base_indent}"asr_service_configs": [']

        asr_configs = config_dict.get('asr_service_configs', [])
        for i, config in enumerate(asr_configs):
            lines.append(f'{item_indent}{{')

            # 基础字段
            lines.append(f'{field_indent}"id": "{config.get("id", "")}",')
            lines.append(f'{field_indent}"service_name": "{config.get("service_name", "")}",')
            lines.append(f'{field_indent}"enabled": {str(config.get("enabled", False)).lower()},')
            lines.append(f'{field_indent}"api_key": "{config.get("api_key", "")}",')

            # 根据服务类型添加特定字段
            service_name = config.get("service_name", "")
            if service_name == "elevenlabs":
                lines.append(f'{field_indent}"language": "{config.get("language", "auto")}",')
                lines.append(f'{field_indent}"num_speakers": {config.get("num_speakers", 0)},')
                lines.append(f'{field_indent}"tag_audio_events": {str(config.get("tag_audio_events", False)).lower()},')
                lines.append(f'{field_indent}"model_id": "{config.get("model_id", "scribe_v1")}",')
                lines.append(f'{field_indent}"enable_diarization": {str(config.get("enable_diarization", True)).lower()},')
                # timestamps_granularity 已在ASR服务层面固定，无需写入配置文件
                lines.append(f'{field_indent}"mode": "{config.get("mode", "free")}"')
            elif service_name == "assemblyai":
                lines.append(f'{field_indent}"speech_model": "{config.get("speech_model", "universal")}",')
                lines.append(f'{field_indent}"language_code": "{config.get("language_code", "auto")}",')
                lines.append(f'{field_indent}"language_detection": {str(config.get("language_detection", True)).lower()},')
                lines.append(f'{field_indent}"speaker_labels": {str(config.get("speaker_labels", True)).lower()},')
                lines.append(f'{field_indent}"speakers_expected": {config.get("speakers_expected", 0)},')
                lines.append(f'{field_indent}"punctuate": {str(config.get("punctuate", True)).lower()},')
                lines.append(f'{field_indent}"format_text": {str(config.get("format_text", True)).lower()},')
                lines.append(f'{field_indent}"disfluencies": {str(config.get("disfluencies", False)).lower()},')
                lines.append(f'{field_indent}"sentiment_analysis": {str(config.get("sentiment_analysis", False)).lower()},')
                lines.append(f'{field_indent}"entity_detection": {str(config.get("entity_detection", False)).lower()},')
                lines.append(f'{field_indent}"auto_highlights": {str(config.get("auto_highlights", False)).lower()},')
                lines.append(f'{field_indent}"auto_chapters": {str(config.get("auto_chapters", False)).lower()},')
                lines.append(f'{field_indent}"summarization": {str(config.get("summarization", False)).lower()}')
            elif service_name == "deepgram":
                lines.append(f'{field_indent}"model": "{config.get("model", "nova-3")}",')
                lines.append(f'{field_indent}"language": "{config.get("language", "auto")}"')

            # 结束当前配置对象
            if i < len(asr_configs) - 1:
                lines.append(f'{item_indent}}},')
            else:
                lines.append(f'{item_indent}}}')

        lines.append(f'{base_indent}],')
        return lines

    def _format_value(self, value: Any) -> str:
        """格式化值为JSON字符串"""
        if isinstance(value, bool):
            return str(value).lower()
        elif isinstance(value, str):

            escaped = value.replace('\\', '\\\\').replace('"', '\\"').replace('\n', '\\n')
            return f'"{escaped}"'
        elif isinstance(value, (int, float)):
            return str(value)
        else:
            return json.dumps(value, ensure_ascii=False)

    def _is_llm_api_configs_last_field(self, config_dict: Dict[str, Any]) -> bool:
        """判断llm_api_configs是否是最后一个字段"""
        # 检查llm_api_configs之后的sections（调试配置）
        if len(self.sections) > 4:  # 确保有第5个section
            for section_name, keys in self.sections[4:]:  # 从第5个section开始
                for key in keys:
                    if key in config_dict:
                        return False
        return True
