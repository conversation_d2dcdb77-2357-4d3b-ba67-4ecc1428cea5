#!/usr/bin/env python3
"""
字幕优化器测试脚本
用于测试subtitle_optimizer.py的功能
"""

import os
import sys
import tempfile
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from subtitle_optimizer import SubtitleOptimizer, SilenceSegment, SubtitleEntry


def test_srt_time_conversion():
    """测试SRT时间格式转换"""
    print("测试SRT时间格式转换...")
    
    optimizer = SubtitleOptimizer()
    
    # 测试用例
    test_cases = [
        ("00:00:12,345", 12.345),
        ("00:01:23,456", 83.456),
        ("01:23:45,678", 5025.678),
        ("00:00:00,001", 0.001),
        ("00:00:00,999", 0.999),
    ]
    
    for srt_time, expected_seconds in test_cases:
        # 测试SRT到秒数的转换
        actual_seconds = optimizer._srt_time_to_seconds(srt_time)
        assert abs(actual_seconds - expected_seconds) < 0.001, \
            f"SRT到秒数转换失败: {srt_time} -> {actual_seconds}, 期望: {expected_seconds}"
        
        # 测试秒数到SRT的转换
        actual_srt = optimizer._seconds_to_srt_time(expected_seconds)
        assert actual_srt == srt_time, \
            f"秒数到SRT转换失败: {expected_seconds} -> {actual_srt}, 期望: {srt_time}"
    
    print("✓ SRT时间格式转换测试通过")


def test_silence_parsing():
    """测试静音段解析"""
    print("测试静音段解析...")
    
    optimizer = SubtitleOptimizer()
    
    # 模拟FFmpeg输出
    test_output = """
[silencedetect @ 0x7f8b8c000000] silence_start: 5.123
[silencedetect @ 0x7f8b8c000000] silence_end: 7.456 | silence_duration: 2.333
[silencedetect @ 0x7f8b8c000000] silence_start: 12.789
[silencedetect @ 0x7f8b8c000000] silence_end: 15.012 | silence_duration: 2.223
[silencedetect @ 0x7f8b8c000000] silence_start: 20.500
"""
    
    silences = optimizer._parse_silence_output(test_output)
    
    # 验证解析结果
    expected_silences = [
        SilenceSegment(5.123, 7.456),
        SilenceSegment(12.789, 15.012),
    ]
    
    assert len(silences) == len(expected_silences), \
        f"静音段数量不匹配: {len(silences)} vs {len(expected_silences)}"
    
    for actual, expected in zip(silences, expected_silences):
        assert abs(actual.start - expected.start) < 0.001, \
            f"静音段开始时间不匹配: {actual.start} vs {expected.start}"
        assert abs(actual.end - expected.end) < 0.001, \
            f"静音段结束时间不匹配: {actual.end} vs {expected.end}"
    
    print("✓ 静音段解析测试通过")


def test_boundary_optimization():
    """测试边界优化算法"""
    print("测试边界优化算法...")
    
    optimizer = SubtitleOptimizer()
    
    # 测试静音段
    silences = [
        SilenceSegment(5.0, 7.0),    # 静音段1
        SilenceSegment(12.0, 15.0),  # 静音段2
        SilenceSegment(20.0, 22.0),  # 静音段3
    ]
    
    # 测试前向搜索
    test_cases_start = [
        (6.0, 7.0),   # 在静音段1中，应该找到7.0（静音结束点）
        (8.0, 8.0),   # 在语音中，保持原时间
        (13.0, 15.0), # 在静音段2中，应该找到15.0（静音结束点）
        (16.0, 16.0), # 在语音中，保持原时间
        (21.0, 22.0), # 在静音段3中，应该找到22.0（静音结束点）
        (25.0, 25.0), # 在语音中，保持原时间
    ]
    
    for start_time, expected in test_cases_start:
        result = optimizer._find_speech_start(start_time, silences)
        assert abs(result - expected) < 0.001, \
            f"前向搜索失败: start_time={start_time}, result={result}, expected={expected}"
    
    # 测试后向搜索
    test_cases_end = [
        (6.0, 5.0),   # 在静音段1中，应该找到5.0（静音开始点）
        (10.0, 10.0), # 在语音中，保持原时间
        (13.0, 12.0), # 在静音段2中，应该找到12.0（静音开始点）
        (18.0, 18.0), # 在语音中，保持原时间
        (21.0, 20.0), # 在静音段3中，应该找到20.0（静音开始点）
        (25.0, 25.0), # 在语音中，保持原时间
    ]
    
    for end_time, expected in test_cases_end:
        result = optimizer._find_speech_end(end_time, silences)
        assert abs(result - expected) < 0.001, \
            f"后向搜索失败: end_time={end_time}, result={result}, expected={expected}"
    
    print("✓ 边界优化算法测试通过")


def test_srt_parsing():
    """测试SRT文件解析"""
    print("测试SRT文件解析...")
    
    # 创建测试SRT内容
    test_srt_content = """1
00:00:10,000 --> 00:00:13,500
Hello world

2
00:00:15,000 --> 00:00:18,200
How are you?

3
00:00:20,100 --> 00:00:25,300
This is a test
with multiple lines
"""
    
    # 写入临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.srt', delete=False, encoding='utf-8') as f:
        f.write(test_srt_content)
        temp_file = f.name
    
    try:
        optimizer = SubtitleOptimizer()
        entries = optimizer.parse_srt_file(temp_file)
        
        # 验证解析结果
        assert len(entries) == 3, f"字幕条目数量不匹配: {len(entries)}"
        
        # 验证第一个条目
        entry1 = entries[0]
        assert entry1.index == 1
        assert abs(entry1.start_time - 10.0) < 0.001
        assert abs(entry1.end_time - 13.5) < 0.001
        assert entry1.text == "Hello world"
        
        # 验证第三个条目（多行文本）
        entry3 = entries[2]
        assert entry3.index == 3
        assert "This is a test\nwith multiple lines" in entry3.text
        
        print("✓ SRT文件解析测试通过")
        
    finally:
        # 清理临时文件
        os.unlink(temp_file)


def test_integration():
    """集成测试"""
    print("运行集成测试...")
    
    # 模拟完整的处理流程
    optimizer = SubtitleOptimizer()
    
    # 模拟静音段
    silences = [
        SilenceSegment(9.5, 10.2),   # 字幕1前的静音
        SilenceSegment(13.0, 13.8), # 字幕1后的静音
        SilenceSegment(14.5, 15.3), # 字幕2前的静音
        SilenceSegment(17.8, 18.5), # 字幕2后的静音
    ]
    
    # 模拟字幕条目
    entries = [
        SubtitleEntry(1, 10.0, 13.5, "Hello world"),
        SubtitleEntry(2, 15.0, 18.0, "How are you?"),
    ]
    
    # 执行优化
    optimized = optimizer.optimize_subtitle_timing(silences, entries)
    
    # 验证结果
    assert len(optimized) == 2
    
    # 验证第一个条目的优化
    opt1 = optimized[0]
    assert abs(opt1.start_time - 10.2) < 0.001, f"第一个条目开始时间: {opt1.start_time}"
    assert abs(opt1.end_time - 13.0) < 0.001, f"第一个条目结束时间: {opt1.end_time}"
    
    # 验证第二个条目的优化
    opt2 = optimized[1]
    assert abs(opt2.start_time - 15.3) < 0.001, f"第二个条目开始时间: {opt2.start_time}"
    assert abs(opt2.end_time - 17.8) < 0.001, f"第二个条目结束时间: {opt2.end_time}"
    
    print("✓ 集成测试通过")


def test_expand_subtitles():
    """测试字幕扩充功能"""
    print("测试字幕扩充功能...")

    optimizer = SubtitleOptimizer()

    # 测试用例1：基本扩充
    subtitles = [
        SubtitleEntry(1, 10.0, 12.0, "Hello"),
        SubtitleEntry(2, 15.0, 17.0, "World"),
    ]

    expanded = optimizer.expand_subtitles(subtitles, 200)

    # 验证扩充结果
    assert len(expanded) == 2

    # 第一个字幕应该扩充200ms
    assert abs(expanded[0].start_time - 9.8) < 0.001, f"第一个字幕开始时间: {expanded[0].start_time}"
    assert abs(expanded[0].end_time - 12.2) < 0.001, f"第一个字幕结束时间: {expanded[0].end_time}"

    # 第二个字幕应该扩充200ms
    assert abs(expanded[1].start_time - 14.8) < 0.001, f"第二个字幕开始时间: {expanded[1].start_time}"
    assert abs(expanded[1].end_time - 17.2) < 0.001, f"第二个字幕结束时间: {expanded[1].end_time}"

    print("✓ 基本扩充测试通过")

    # 测试用例2：重叠冲突解决
    close_subtitles = [
        SubtitleEntry(1, 10.0, 11.0, "Hello"),
        SubtitleEntry(2, 11.1, 12.1, "World"),  # 间隔100ms
    ]

    expanded_close = optimizer.expand_subtitles(close_subtitles, 200)

    # 验证冲突解决
    assert expanded_close[0].end_time <= expanded_close[1].start_time, \
        f"重叠未解决: {expanded_close[0].end_time} > {expanded_close[1].start_time}"

    # 验证中点分割
    expected_midpoint = (11.2 + 10.9) / 2  # (扩充后的结束时间 + 扩充后的开始时间) / 2
    assert abs(expanded_close[0].end_time - expected_midpoint) < 0.001
    assert abs(expanded_close[1].start_time - expected_midpoint) < 0.001

    print("✓ 冲突解决测试通过")

    # 测试用例3：边界情况（开头）
    early_subtitle = [
        SubtitleEntry(1, 0.1, 1.0, "Start"),
    ]

    expanded_early = optimizer.expand_subtitles(early_subtitle, 200)

    # 开始时间不能小于0
    assert expanded_early[0].start_time >= 0, f"开始时间为负: {expanded_early[0].start_time}"
    assert expanded_early[0].start_time == 0, f"开始时间应为0: {expanded_early[0].start_time}"

    print("✓ 边界情况测试通过")

    # 测试用例4：禁用扩充
    no_expand = optimizer.expand_subtitles(subtitles, 0)

    # 应该返回原始字幕
    assert no_expand == subtitles, "禁用扩充时应返回原始字幕"

    print("✓ 禁用扩充测试通过")

    # 测试用例5：相同时间戳边界情况
    edge_case_subtitles = [
        SubtitleEntry(1, 10.0, 12.0, "First"),
        SubtitleEntry(2, 12.0, 14.0, "Second"),  # 开始时间等于前一个结束时间
    ]

    expanded_edge = optimizer.expand_subtitles(edge_case_subtitles, 200)

    # 验证相同时间戳被正确处理
    assert expanded_edge[0].end_time < expanded_edge[1].start_time, \
        f"相同时间戳未正确处理: {expanded_edge[0].end_time} >= {expanded_edge[1].start_time}"

    # 验证中点分割
    original_boundary = 12.0
    actual_gap = expanded_edge[1].start_time - expanded_edge[0].end_time
    assert actual_gap > 0, f"应该有间隔，但间隔为: {actual_gap}"

    print("✓ 相同时间戳边界测试通过")

    print("✓ 字幕扩充功能测试通过")


def test_bridge_gaps():
    """测试桥接短间隔功能"""
    print("测试桥接短间隔功能...")

    optimizer = SubtitleOptimizer()

    # 测试用例1：基本桥接功能
    basic_subtitles = [
        SubtitleEntry(1, 10.0, 12.0, "Hello"),
        SubtitleEntry(2, 12.2, 14.0, "World"),  # 200ms间隔
        SubtitleEntry(3, 14.5, 16.0, "Test"),   # 500ms间隔，不应桥接
    ]

    bridged = optimizer.bridge_gaps(basic_subtitles, 300)  # 300ms阈值

    # 验证第一个间隔被桥接
    assert bridged[0].end_time == 12.1, f"第一个字幕结束时间应为12.1，实际为{bridged[0].end_time}"
    assert bridged[1].start_time == 12.1, f"第二个字幕开始时间应为12.1，实际为{bridged[1].start_time}"

    # 验证第二个间隔未被桥接（超过阈值）
    assert bridged[1].end_time == 14.0, f"第二个字幕结束时间应保持14.0，实际为{bridged[1].end_time}"
    assert bridged[2].start_time == 14.5, f"第三个字幕开始时间应保持14.5，实际为{bridged[2].start_time}"

    print("✓ 基本桥接测试通过")

    # 测试用例2：边界情况
    edge_case_subtitles = [
        SubtitleEntry(1, 10.0, 12.0, "First"),
        SubtitleEntry(2, 12.0, 14.0, "Second"),  # 零间隔
        SubtitleEntry(3, 13.8, 16.0, "Third"),   # 负间隔（重叠）
    ]

    bridged_edge = optimizer.bridge_gaps(edge_case_subtitles, 300)

    # 验证零间隔和负间隔不被桥接
    assert bridged_edge[0].end_time == 12.0, "零间隔不应被桥接"
    assert bridged_edge[1].start_time == 12.0, "零间隔不应被桥接"
    assert bridged_edge[1].end_time == 14.0, "重叠情况不应被桥接"
    assert bridged_edge[2].start_time == 13.8, "重叠情况不应被桥接"

    print("✓ 边界情况测试通过")

    # 测试用例3：禁用桥接
    disabled_test_subtitles = [
        SubtitleEntry(1, 10.0, 12.0, "Hello"),
        SubtitleEntry(2, 12.2, 14.0, "World"),  # 200ms间隔
    ]
    disabled_bridged = optimizer.bridge_gaps(disabled_test_subtitles, 0)

    # 验证禁用时不进行桥接
    assert disabled_bridged[0].end_time == 12.0, "禁用桥接时应保持原始时间"
    assert disabled_bridged[1].start_time == 12.2, "禁用桥接时应保持原始时间"

    print("✓ 禁用桥接测试通过")

    # 测试用例4：连续短间隔
    continuous_subtitles = [
        SubtitleEntry(1, 10.0, 11.0, "A"),
        SubtitleEntry(2, 11.1, 12.0, "B"),  # 100ms间隔
        SubtitleEntry(3, 12.2, 13.0, "C"),  # 200ms间隔
    ]

    continuous_bridged = optimizer.bridge_gaps(continuous_subtitles, 300)

    # 验证连续间隔都被桥接
    assert continuous_bridged[0].end_time == 11.05, "第一个间隔应被桥接"
    assert continuous_bridged[1].start_time == 11.05, "第一个间隔应被桥接"
    assert continuous_bridged[1].end_time == 12.1, "第二个间隔应被桥接"
    assert continuous_bridged[2].start_time == 12.1, "第二个间隔应被桥接"

    print("✓ 连续短间隔测试通过")

    # 测试用例5：最小间隔阈值（2ms以下不桥接）
    tiny_gap_subtitles = [
        SubtitleEntry(1, 10.0, 12.0, "First"),
        SubtitleEntry(2, 12.001, 14.0, "Second"),  # 1ms间隔，不应桥接
        SubtitleEntry(3, 14.005, 16.0, "Third"),   # 5ms间隔，应该桥接
    ]

    tiny_gap_bridged = optimizer.bridge_gaps(tiny_gap_subtitles, 300)

    # 验证1ms间隔不被桥接
    assert tiny_gap_bridged[0].end_time == 12.0, "1ms间隔不应被桥接"
    assert tiny_gap_bridged[1].start_time == 12.001, "1ms间隔不应被桥接"

    # 验证5ms间隔被桥接
    assert abs(tiny_gap_bridged[1].end_time - 14.0025) < 0.0001, f"5ms间隔应被桥接，实际: {tiny_gap_bridged[1].end_time}"
    assert abs(tiny_gap_bridged[2].start_time - 14.0025) < 0.0001, f"5ms间隔应被桥接，实际: {tiny_gap_bridged[2].start_time}"

    print("✓ 最小间隔阈值测试通过")

    print("✓ 桥接短间隔功能测试通过")


def main():
    """运行所有测试"""
    print("开始运行字幕优化器测试...")
    print("=" * 50)
    
    try:
        test_srt_time_conversion()
        test_silence_parsing()
        test_boundary_optimization()
        test_srt_parsing()
        test_integration()
        test_expand_subtitles()
        test_bridge_gaps()

        print("=" * 50)
        print("✓ 所有测试通过！")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
