j#!/usr/bin/env python3
"""
音频转AAC批量转换器
基于EvaTrans项目audio_processor.py设计的独立脚本

功能特性：
- 递归扫描所有音频/视频文件
- 转换为256k AAC格式（高质量立体声）
- 多声道降混处理（复用原项目逻辑）
- 实时进度条显示（FFmpeg进度解析）
- 完整的日志记录和错误处理
- 输出到aac_output目录，保持原有结构

依赖：
- pip install tqdm
- 系统需要安装FFmpeg

作者：基于EvaTrans项目设计
"""

import os
import subprocess
import json
import time
import logging
import re
from pathlib import Path
from typing import List, Optional, Tuple, Dict
from dataclasses import dataclass
from datetime import datetime

from tqdm import tqdm

# ============================================================================
# 配置常量
# ============================================================================

# FFmpeg路径和参数
FFMPEG_PATH = "ffmpeg"
FFPROBE_PATH = "ffprobe"
AAC_BITRATE = "256k"  # 256kbps高质量AAC
THREADS = "4"
TIMEOUT = 3600  # FFmpeg超时（秒）
PROBE_TIMEOUT = 30  # FFprobe超时（秒）

# 输出目录名称
OUTPUT_DIR_NAME = "aac_output"

# 支持的音频/视频文件扩展名（复用原项目列表）
SUPPORTED_EXTENSIONS = {
    # 视频格式
    ".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm", ".m4v", ".3gp", ".3g2",
    ".mpg", ".mpeg", ".m2v", ".m4p", ".m4b", ".f4v", ".f4p", ".f4a", ".f4b",
    ".vob", ".ogv", ".ogg", ".drc", ".gif", ".gifv", ".mng", ".qt",
    ".yuv", ".rm", ".rmvb", ".asf", ".amv", ".mp2", ".mpe", ".mpv", ".svi", 
    ".mxf", ".roq", ".nsv", 
    # 音频格式
    ".wav", ".mp3", ".flac", ".aac", ".m4a", ".opus", ".aiff", ".au",
    ".ra", ".3ga", ".amr", ".awb", ".dct", ".dss", ".dvf", ".gsm", ".iklax", ".ivs",
    ".mmf", ".mpc", ".msv", ".nmf", ".oga", ".mogg", ".raw", ".sln", ".tta",
    ".voc", ".vox", ".wv", ".8svx", ".cda", ".wma"
}

# 复用原项目的专业声道降混滤镜映射表（基于音频工程学原理）
CHANNEL_FILTERS = {
    1: None,  # 单声道保持不变
    2: None,  # 立体声保持不变
    3: "pan=stereo|FL=FL+0.5*LFE|FR=FR+0.5*LFE",  # 2.1环绕
    4: "pan=stereo|FL=0.7*FL+0.3*BL|FR=0.7*FR+0.3*BR",  # 4.0环绕
    5: "pan=stereo|FL=0.6*FL+0.3*BL+0.4*LFE|FR=0.6*FR+0.3*BR+0.4*LFE",  # 4.1环绕
    6: "pan=stereo|FL=0.5*FL+0.707*FC+0.5*BL+0.3*LFE|FR=0.5*FR+0.707*FC+0.5*BR+0.3*LFE",  # 5.1环绕
    7: "pan=stereo|FL=0.4*FL+0.6*FC+0.4*BL+0.1*BC+0.3*LFE|FR=0.4*FR+0.6*FC+0.4*BR+0.1*BC+0.3*LFE",  # 6.1环绕
    8: "pan=stereo|FL=0.4*FL+0.6*FC+0.4*BL+0.2*SL+0.3*LFE|FR=0.4*FR+0.6*FC+0.4*BR+0.2*SR+0.3*LFE",  # 7.1环绕
}

# 超过8声道的降级处理滤镜（兜底策略）
FALLBACK_FILTER = "pan=stereo|FL=0.4*FL+0.6*FC+0.4*BL+0.2*SL+0.3*LFE|FR=0.4*FR+0.6*FC+0.4*BR+0.2*SR+0.3*LFE"

# ============================================================================
# 数据模型
# ============================================================================

@dataclass
class ConversionResult:
    """单个文件转换结果"""
    success: bool
    input_path: str
    output_path: str
    processing_time: float
    error_message: str = ""
    input_size: int = 0
    output_size: int = 0
    channels: int = 0
    duration: Optional[float] = None

@dataclass
class ConversionStats:
    """转换统计信息"""
    total_files: int = 0
    success_count: int = 0
    failed_count: int = 0
    total_time: float = 0.0
    total_input_size: int = 0
    total_output_size: int = 0
    failed_files: List[str] = None
    
    def __post_init__(self):
        if self.failed_files is None:
            self.failed_files = []

# ============================================================================
# 核心转换类
# ============================================================================

class AacConverter:
    """AAC转换器 - 基于原项目audio_processor.py设计"""

    def __init__(self):
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志记录"""
        log_filename = f"aac_conversion_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"开始AAC转换任务，日志文件：{log_filename}")
    
    def check_ffmpeg(self) -> bool:
        """检查FFmpeg是否可用"""
        try:
            subprocess.run([FFMPEG_PATH, "-version"], 
                         capture_output=True, check=True, timeout=10)
            subprocess.run([FFPROBE_PATH, "-version"], 
                         capture_output=True, check=True, timeout=10)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
            return False
    
    def scan_audio_files(self, root_dir: str) -> List[str]:
        """递归扫描音频/视频文件"""
        audio_files = []
        root_path = Path(root_dir)
        
        for file_path in root_path.rglob("*"):
            if file_path.is_file() and file_path.suffix.lower() in SUPPORTED_EXTENSIONS:
                # 跳过输出目录中的文件
                if OUTPUT_DIR_NAME not in file_path.parts:
                    audio_files.append(str(file_path))
        
        return sorted(audio_files)
    
    def probe_audio_info(self, file_path: str) -> Tuple[int, Optional[float]]:
        """使用FFprobe检测音频信息（复用原项目逻辑）"""
        try:
            cmd = [
                FFPROBE_PATH,
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_streams',
                '-select_streams', 'a:0',
                file_path
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=False,
                timeout=PROBE_TIMEOUT
            )
            
            stdout_text = result.stdout.decode('utf-8', errors='ignore') if result.stdout else ""
            stderr_text = result.stderr.decode('utf-8', errors='ignore') if result.stderr else ""
            
            if result.returncode != 0:
                raise Exception(f"FFprobe失败: {stderr_text}")
            
            data = json.loads(stdout_text)
            streams = data.get('streams', [])
            if not streams:
                raise Exception("未找到音频流")
            
            audio_stream = streams[0]
            channels = audio_stream.get('channels', 2)
            duration_str = audio_stream.get('duration')
            duration = float(duration_str) if duration_str else None
            
            return channels, duration
            
        except Exception as e:
            raise Exception(f"音频信息检测失败: {str(e)}")

    def get_file_duration(self, file_path: str) -> Optional[float]:
        """获取文件精确时长（用于进度计算）"""
        try:
            cmd = [
                FFPROBE_PATH,
                '-v', 'quiet',
                '-show_entries', 'format=duration',
                '-of', 'csv=p=0',
                file_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode != 0:
                return None

            duration_str = result.stdout.strip()
            if duration_str and duration_str != 'N/A':
                return float(duration_str)

            return None

        except (subprocess.TimeoutExpired, ValueError, Exception):
            return None

    def parse_ffmpeg_progress(self, line: str, total_duration: float) -> Optional[float]:
        """解析FFmpeg输出中的进度信息"""
        try:
            # 解析 time=00:01:23.45 格式
            time_match = re.search(r'time=(\d+):(\d+):(\d+\.\d+)', line)
            if time_match:
                hours, minutes, seconds = time_match.groups()
                current_seconds = int(hours) * 3600 + int(minutes) * 60 + float(seconds)
                if total_duration > 0:
                    progress = (current_seconds / total_duration) * 100
                    return min(progress, 100.0)  # 确保不超过100%
            return None
        except (ValueError, AttributeError):
            return None

    def get_channel_filter(self, channels: int) -> Optional[str]:
        """获取声道处理滤镜（复用原项目逻辑）"""
        if channels <= 2:
            return None
        elif channels <= 8:
            return CHANNEL_FILTERS.get(channels)
        else:
            return FALLBACK_FILTER
    
    def convert_to_aac(self, input_path: str, output_path: str,
                       channel_filter: Optional[str],
                       progress_callback=None, total_duration: float = 0) -> None:
        """转换音频为256k AAC（支持实时进度回调）"""
        try:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 构建FFmpeg命令
            cmd = [
                FFMPEG_PATH,
                '-i', input_path,
                '-c:a', 'aac',         # 使用FFmpeg原生AAC编码器
                '-b:a', AAC_BITRATE,   # 256k比特率
                '-ac', '2',  # 立体声（降混后）
                '-threads', THREADS,  # 4线程并行
                '-y',  # 覆盖输出文件
            ]

            # 添加声道处理滤镜（如果需要）
            if channel_filter:
                cmd.extend(['-af', channel_filter])

            # 添加输出文件路径
            cmd.append(output_path)

            # 如果有进度回调且有总时长，使用实时进度模式
            if progress_callback and total_duration > 0:
                self._convert_with_progress(cmd, progress_callback, total_duration)
            else:
                # 降级到原始模式
                self._convert_simple(cmd)

        except subprocess.TimeoutExpired:
            raise Exception(f"转换超时（{TIMEOUT}秒）")
        except Exception as e:
            raise Exception(f"转换失败: {str(e)}")

    def _convert_with_progress(self, cmd: List[str], progress_callback, total_duration: float):
        """使用实时进度的转换方法"""
        process = subprocess.Popen(
            cmd,
            stderr=subprocess.STDOUT,  # 重定向stderr到stdout
            stdout=subprocess.PIPE,
            universal_newlines=True,   # 关键：处理换行符
            bufsize=1                  # 行缓冲
        )

        try:
            last_progress = 0
            for line in process.stdout:
                # 解析进度信息
                progress = self.parse_ffmpeg_progress(line, total_duration)
                if progress is not None and progress > last_progress:
                    progress_callback(progress)
                    last_progress = progress

            # 等待进程完成
            return_code = process.wait(timeout=TIMEOUT)
            if return_code != 0:
                raise Exception(f"FFmpeg转换失败，返回码: {return_code}")

        except subprocess.TimeoutExpired:
            process.kill()
            raise Exception(f"转换超时（{TIMEOUT}秒）")
        finally:
            if process.poll() is None:
                process.terminate()

    def _convert_simple(self, cmd: List[str]):
        """简单转换方法（降级模式）"""
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=False,
            timeout=TIMEOUT
        )

        stderr_text = result.stderr.decode('utf-8', errors='ignore') if result.stderr else ""

        if result.returncode != 0:
            raise Exception(f"FFmpeg转换失败: {stderr_text}")

    def convert_file(self, input_path: str, output_dir: str) -> ConversionResult:
        """转换单个文件"""
        start_time = time.time()

        # 生成输出路径，保持目录结构
        rel_path = os.path.relpath(input_path, ".")
        output_path = os.path.join(output_dir, rel_path)
        output_path = os.path.splitext(output_path)[0] + ".m4a"

        try:
            # 获取输入文件大小
            input_size = os.path.getsize(input_path)

            # 检测音频信息
            channels, duration = self.probe_audio_info(input_path)

            # 生成声道处理滤镜
            channel_filter = self.get_channel_filter(channels)

            # 执行转换
            self.convert_to_aac(input_path, output_path, channel_filter)

            # 获取输出文件大小
            output_size = os.path.getsize(output_path)
            processing_time = time.time() - start_time

            # 记录成功日志
            self.logger.info(f"转换成功: {os.path.basename(input_path)} -> {os.path.basename(output_path)} "
                           f"({processing_time:.1f}s, {channels}ch, 256k)")

            return ConversionResult(
                success=True,
                input_path=input_path,
                output_path=output_path,
                processing_time=processing_time,
                input_size=input_size,
                output_size=output_size,
                channels=channels,
                duration=duration
            )

        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = str(e)

            # 记录失败日志
            self.logger.error(f"转换失败: {os.path.basename(input_path)} - {error_msg}")

            return ConversionResult(
                success=False,
                input_path=input_path,
                output_path=output_path,
                processing_time=processing_time,
                error_message=error_msg
            )

    def convert_file_with_progress(self, input_path: str, output_dir: str,
                                 progress_callback=None) -> ConversionResult:
        """转换单个文件（支持进度回调）"""
        start_time = time.time()

        # 生成输出路径，保持目录结构
        rel_path = os.path.relpath(input_path, ".")
        output_path = os.path.join(output_dir, rel_path)
        output_path = os.path.splitext(output_path)[0] + ".m4a"

        try:
            # 获取输入文件大小
            input_size = os.path.getsize(input_path)

            # 检测音频信息
            channels, duration = self.probe_audio_info(input_path)

            # 获取精确时长用于进度计算
            total_duration = self.get_file_duration(input_path) or duration or 0

            # 生成声道处理滤镜
            channel_filter = self.get_channel_filter(channels)

            # 执行转换（带进度回调）
            self.convert_to_aac(input_path, output_path, channel_filter,
                              progress_callback, total_duration)

            # 获取输出文件大小
            output_size = os.path.getsize(output_path)
            processing_time = time.time() - start_time

            # 记录成功日志
            self.logger.info(f"转换成功: {os.path.basename(input_path)} -> {os.path.basename(output_path)} "
                           f"({processing_time:.1f}s, {channels}ch, 256k)")

            return ConversionResult(
                success=True,
                input_path=input_path,
                output_path=output_path,
                processing_time=processing_time,
                input_size=input_size,
                output_size=output_size,
                channels=channels,
                duration=duration
            )

        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = str(e)

            # 记录失败日志
            self.logger.error(f"转换失败: {os.path.basename(input_path)} - {error_msg}")

            return ConversionResult(
                success=False,
                input_path=input_path,
                output_path=output_path,
                processing_time=processing_time,
                input_size=input_size,
                output_size=0,
                channels=0,
                duration=0,
                error_message=error_msg
            )

    def batch_convert(self, files: List[str], output_dir: str) -> ConversionStats:
        """批量转换文件，使用双层进度条显示"""
        stats = ConversionStats(total_files=len(files))

        # 外层进度条：文件级进度
        with tqdm(
            files,
            desc="转换音频文件",
            unit="文件",
            ncols=100,
            bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]'
        ) as file_pbar:

            for file_path in file_pbar:
                filename = os.path.basename(file_path)
                file_pbar.set_description(f"处理: {filename}")

                # 内层进度条：当前文件转换进度
                current_progress = [0]  # 使用列表来在闭包中修改值

                def progress_callback(progress):
                    """进度回调函数"""
                    current_progress[0] = progress
                    # 更新文件级进度条的后缀信息
                    file_pbar.set_postfix({
                        '当前': f"{progress:.0f}%",
                        '成功': stats.success_count,
                        '失败': stats.failed_count
                    })

                # 执行转换（带实时进度）
                result = self.convert_file_with_progress(file_path, output_dir, progress_callback)

                # 更新统计信息
                if result.success:
                    stats.success_count += 1
                    stats.total_input_size += result.input_size
                    stats.total_output_size += result.output_size
                    # 使用tqdm.write输出不干扰进度条的消息
                    tqdm.write(f"✓ {filename} (100%)")
                else:
                    stats.failed_count += 1
                    stats.failed_files.append(file_path)
                    # 输出失败信息
                    tqdm.write(f"✗ {filename} - {result.error_message}")

                # 更新文件级进度条的最终状态
                file_pbar.set_postfix({
                    '成功': stats.success_count,
                    '失败': stats.failed_count
                })

        return stats



# ============================================================================
# 工具函数
# ============================================================================

def format_file_size(size_bytes: int) -> str:
    """格式化文件大小显示"""
    if size_bytes == 0:
        return "0 B"

    units = ['B', 'KB', 'MB', 'GB', 'TB']
    unit_index = 0
    size = float(size_bytes)

    while size >= 1024 and unit_index < len(units) - 1:
        size /= 1024
        unit_index += 1

    return f"{size:.1f} {units[unit_index]}"

def format_duration(seconds: float) -> str:
    """格式化时间显示"""
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = int(seconds % 60)
        return f"{minutes}分{secs}秒"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        return f"{hours}小时{minutes}分钟"

def display_summary_report(stats: ConversionStats, total_time: float):
    """显示总结报告"""
    print("\n" + "="*50)
    print("转换完成报告")
    print("="*50)

    print(f"总文件数: {stats.total_files}")
    print(f"成功转换: {stats.success_count}")
    print(f"转换失败: {stats.failed_count}")
    print(f"总耗时: {format_duration(total_time)}")

    if stats.total_input_size > 0:
        print(f"输入总大小: {format_file_size(stats.total_input_size)}")
        print(f"输出总大小: {format_file_size(stats.total_output_size)}")

        if stats.total_input_size > 0:
            compression_ratio = (1 - stats.total_output_size / stats.total_input_size) * 100
            print(f"压缩率: {compression_ratio:.1f}%")

    if stats.success_count > 0:
        avg_time = total_time / stats.success_count
        print(f"平均处理时间: {avg_time:.1f}秒/文件")

    # 如果有失败的文件，显示失败列表
    if stats.failed_files:
        print("\n转换失败的文件：")
        for failed_file in stats.failed_files:
            print(f"• {os.path.basename(failed_file)}")

    print("="*50)

# ============================================================================
# 主函数
# ============================================================================

def main():
    """主函数"""
    # 显示欢迎信息
    print("="*60)
    print("音频转AAC批量转换器")
    print("基于EvaTrans项目设计")
    print("支持多种音频/视频格式，智能声道处理")
    print("输出：256k高质量AAC")
    print("="*60)

    # 创建转换器实例
    converter = AacConverter()

    # 检查FFmpeg
    print("检查FFmpeg环境...")
    if not converter.check_ffmpeg():
        print("❌ 错误：未找到FFmpeg或FFprobe，请先安装FFmpeg")
        print("安装指南：https://ffmpeg.org/download.html")
        return
    print("✓ FFmpeg环境检查通过")

    # 扫描文件
    print("扫描音频/视频文件...")
    current_dir = os.getcwd()
    files = converter.scan_audio_files(current_dir)

    if not files:
        print("未找到支持的音频/视频文件")
        return

    print(f"✓ 找到 {len(files)} 个文件待转换")

    # 显示文件类型统计
    extensions = {}
    for file_path in files:
        ext = os.path.splitext(file_path)[1].lower()
        extensions[ext] = extensions.get(ext, 0) + 1

    print("文件类型分布：")
    for ext, count in sorted(extensions.items()):
        print(f"  {ext}: {count} 个文件")

    # 设置输出目录
    output_dir = os.path.join(current_dir, OUTPUT_DIR_NAME)
    print(f"输出目录：{output_dir}")

    # 开始转换
    print("\n开始批量转换...")

    start_time = time.time()
    stats = converter.batch_convert(files, output_dir)
    total_time = time.time() - start_time

    # 显示总结报告
    display_summary_report(stats, total_time)

    # 最终状态
    if stats.failed_count == 0:
        print("\n🎉 所有文件转换完成！")
    else:
        print(f"\n⚠️  转换完成，但有 {stats.failed_count} 个文件失败")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n用户中断转换")
    except Exception as e:
        print(f"\n程序异常：{str(e)}")
