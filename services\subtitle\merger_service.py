"""字幕合并服务

将分段字幕文件合并为完整的字幕文件，支持时间戳校正和序号重排。

核心功能：
- 分段字幕文件检测和排序
- 时间偏移量解析和校正
- SRT格式处理和合并
- 字幕序号重新编排

技术特性：
- 文件名时间偏移量解析
- 毫秒级时间戳精度
- 错误容忍和恢复机制
- 批量处理支持
"""

import os
import re
import logging
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass


logger = logging.getLogger(__name__)


@dataclass
class SubtitleEntry:
    """字幕条目数据结构"""
    index: int
    start_time: str
    end_time: str
    text: str


class SubtitleMergeError(Exception):
    """字幕合并异常"""
    pass


class SubtitleMergerService:
    """字幕合并服务 - 分段字幕文件的智能合并处理

    核心功能：
    - 分段字幕文件检测和分组
    - 时间偏移量计算和校正
    - SRT条目解析和重排
    - 合并结果验证和优化
    """
    
    def __init__(self):
        """初始化字幕合并服务，预编译正则表达式提高性能"""
        self.logger = logging.getLogger(__name__)
        
        # 预编译正则表达式，提高性能
        self.segment_pattern = re.compile(r'(.+)_part(\d+)_(\d{2})-(\d{2})-(\d{2})-(.+)\.srt$')
        self.timecode_pattern = re.compile(r'(\d{2}):(\d{2}):(\d{2}),(\d{3})')
        self.srt_entry_pattern = re.compile(
            r'(\d+)\n(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})\n(.*?)(?=\n\d+\n|\n*$)',
            re.DOTALL
        )
    
    def parse_segment_offset_from_filename(self, filename: str) -> Tuple[float, int, str]:
        """从分段字幕文件名解析时间偏移量信息

        解析格式：{base}_part{N}_{HH}-{MM}-{SS}-{service}-{api}.srt

        Args:
            filename: 分段字幕文件名

        Returns:
            Tuple[float, int, str]: (时间偏移量秒数, 分段编号, 服务API标识)

        Raises:
            SubtitleMergeError: 文件名格式解析失败
        """
        match = self.segment_pattern.match(filename)
        if not match:
            raise SubtitleMergeError(f"无法解析分段文件名格式: {filename}")
        
        base_name, part_num, hours, minutes, seconds, service_api = match.groups()
        
        # 计算时间偏移量（秒）
        offset_seconds = int(hours) * 3600 + int(minutes) * 60 + int(seconds)
        part_number = int(part_num)
        
        self.logger.debug(f"解析文件名 {filename}: 偏移={offset_seconds}s, 分段={part_number}")
        
        return offset_seconds, part_number, service_api
    
    def adjust_srt_timestamps(self, srt_content: str, offset_seconds: float) -> str:
        """校正SRT文件中的所有时间戳

        使用正则表达式匹配时间戳格式，将偏移量添加到每个时间戳上。
        处理格式：HH:MM:SS,mmm --> HH:MM:SS,mmm

        Args:
            srt_content: 原始SRT文件内容
            offset_seconds: 时间偏移量（秒），可为负数

        Returns:
            str: 时间戳校正后的SRT内容
        """
        def adjust_timecode(match):
            h, m, s, ms = map(int, match.groups())
            
            # 转换为总秒数
            total_seconds = h * 3600 + m * 60 + s + ms / 1000.0
            
            # 加上偏移量
            new_total_seconds = total_seconds + offset_seconds
            
            # 重新计算时分秒毫秒
            new_h = int(new_total_seconds // 3600)
            new_m = int((new_total_seconds % 3600) // 60)
            new_s = int(new_total_seconds % 60)
            new_ms = int((new_total_seconds % 1) * 1000)
            
            return f"{new_h:02d}:{new_m:02d}:{new_s:02d},{new_ms:03d}"
        
        # 替换所有时间戳
        adjusted_content = self.timecode_pattern.sub(adjust_timecode, srt_content)
        
        self.logger.debug(f"时间戳校正完成，偏移量: {offset_seconds:.1f}s")
        
        return adjusted_content
    
    def parse_srt_entries(self, srt_content: str) -> List[SubtitleEntry]:
        """解析SRT文件为结构化数据
        
        Args:
            srt_content: SRT文件内容
            
        Returns:
            List[SubtitleEntry]: 字幕条目列表
        """
        entries = []
        matches = self.srt_entry_pattern.findall(srt_content)
        
        for match in matches:
            index, start_time, end_time, text = match
            
            # 清理文本内容
            text = text.strip()
            
            entries.append(SubtitleEntry(
                index=int(index),
                start_time=start_time,
                end_time=end_time,
                text=text
            ))
        
        self.logger.debug(f"解析SRT条目: {len(entries)} 个")
        
        return entries
    
    def generate_merged_srt(self, entries: List[SubtitleEntry]) -> str:
        """生成合并后的SRT内容
        
        Args:
            entries: 字幕条目列表
            
        Returns:
            str: 合并后的SRT内容
        """
        srt_lines = []
        
        for i, entry in enumerate(entries, 1):
            srt_lines.append(str(i))
            srt_lines.append(f"{entry.start_time} --> {entry.end_time}")
            srt_lines.append(entry.text)
            srt_lines.append("")  # 空行分隔
        
        # 移除最后的空行
        if srt_lines and srt_lines[-1] == "":
            srt_lines.pop()
        
        return "\n".join(srt_lines)
    
    def detect_segment_subtitles(self, project_dir: str, base_name: str) -> Dict[str, List[str]]:
        """检测和分组项目目录中的分段字幕文件

        按服务-API组合对分段字幕文件进行分组，便于后续合并处理。

        Args:
            project_dir: 项目目录路径
            base_name: 基础文件名（不含扩展名）

        Returns:
            Dict[str, List[str]]: 按服务API分组的文件列表
        """
        segment_groups = {}
        
        if not os.path.exists(project_dir):
            self.logger.warning(f"项目目录不存在: {project_dir}")
            return segment_groups
        
        # 扫描目录中的字幕文件
        for filename in os.listdir(project_dir):
            if not filename.endswith('.srt'):
                continue
            
            if not filename.startswith(base_name):
                continue
            
            try:
                offset, part_num, service_api = self.parse_segment_offset_from_filename(filename)
                
                if service_api not in segment_groups:
                    segment_groups[service_api] = []
                
                segment_groups[service_api].append(filename)
                
            except SubtitleMergeError:
                # 不是分段文件，跳过
                continue
        
        # 对每组文件按分段编号排序
        for service_api in segment_groups:
            segment_groups[service_api].sort(key=lambda f: self.parse_segment_offset_from_filename(f)[1])
        
        self.logger.info(f"检测到分段字幕组: {list(segment_groups.keys())}")
        
        return segment_groups
    
    def _log_info(self, message: str):
        """记录信息日志"""
        self.logger.info(message)
    
    def _log_debug(self, message: str):
        """记录调试日志"""
        self.logger.debug(message)
    
    def _log_warning(self, message: str):
        """记录警告日志"""
        self.logger.warning(message)
    
    def _log_error(self, message: str):
        """记录错误日志"""
        self.logger.error(message)

    def merge_subtitle_group(self, subtitle_files: List[str], project_dir: str, output_path: str) -> bool:
        """合并一组分段字幕文件为完整字幕

        处理流程：
        1. 解析每个分段文件的时间偏移量
        2. 校正时间戳并解析SRT条目
        3. 按时间顺序排序所有条目
        4. 重新编号并生成合并文件

        Args:
            subtitle_files: 分段字幕文件列表（已排序）
            project_dir: 项目目录路径
            output_path: 合并后的输出文件路径

        Returns:
            bool: 合并操作是否成功
        """
        try:
            self._log_info(f"开始合并 {len(subtitle_files)} 个分段字幕文件")

            all_entries = []

            # 处理每个分段文件
            for i, filename in enumerate(subtitle_files):
                file_path = os.path.join(project_dir, filename)

                if not os.path.exists(file_path):
                    self._log_warning(f"分段文件不存在: {filename}")
                    continue

                try:
                    # 解析偏移量
                    offset_seconds, part_num, service_api = self.parse_segment_offset_from_filename(filename)

                    # 读取文件内容
                    with open(file_path, 'r', encoding='utf-8') as f:
                        srt_content = f.read()

                    # 校正时间戳
                    adjusted_content = self.adjust_srt_timestamps(srt_content, offset_seconds)

                    # 解析字幕条目
                    entries = self.parse_srt_entries(adjusted_content)

                    self._log_debug(f"分段 {part_num}: {len(entries)} 个条目，偏移 {offset_seconds:.1f}s")

                    all_entries.extend(entries)

                except Exception as e:
                    self._log_error(f"处理分段文件失败 {filename}: {e}")
                    continue

            if not all_entries:
                self._log_error("没有有效的字幕条目可合并")
                return False

            # 按时间戳排序（确保时间顺序正确）
            all_entries.sort(key=lambda entry: self._parse_timecode_to_seconds(entry.start_time))

            # 重新编号
            for i, entry in enumerate(all_entries, 1):
                entry.index = i

            # 生成合并后的SRT内容
            merged_content = self.generate_merged_srt(all_entries)

            # 写入输出文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(merged_content)

            self._log_info(f"字幕合并完成: {output_path} ({len(all_entries)} 个条目)")

            return True

        except Exception as e:
            self._log_error(f"字幕合并失败: {e}")
            return False

    def _parse_timecode_to_seconds(self, timecode: str) -> float:
        """将SRT时间码转换为秒数，用于时间排序

        Args:
            timecode: SRT格式时间码 (HH:MM:SS,mmm)

        Returns:
            float: 转换后的总秒数
        """
        match = self.timecode_pattern.match(timecode)
        if not match:
            return 0.0

        h, m, s, ms = map(int, match.groups())
        return h * 3600 + m * 60 + s + ms / 1000.0

    def merge_all_segment_subtitles(self, project_dir: str, base_name: str) -> Dict[str, bool]:
        """批量合并所有ASR-LLM组合的分段字幕文件

        自动检测所有分段字幕组合，逐个执行合并操作。

        Args:
            project_dir: 项目目录路径
            base_name: 基础文件名（不含扩展名）

        Returns:
            Dict[str, bool]: 各组合的合并成功状态
        """
        results = {}

        # 检测分段字幕组
        segment_groups = self.detect_segment_subtitles(project_dir, base_name)

        if not segment_groups:
            self._log_info("未检测到分段字幕文件")
            return results

        # 处理每个组合
        for service_api, files in segment_groups.items():
            if len(files) < 2:
                self._log_warning(f"{service_api}: 只有 {len(files)} 个分段，跳过合并")
                results[service_api] = False
                continue

            # 生成输出文件名
            output_filename = f"{base_name}-{service_api}.srt"
            output_path = os.path.join(project_dir, output_filename)

            # 执行合并
            success = self._safe_merge_with_fallback(files, project_dir, output_path)
            results[service_api] = success

            if success:
                self._log_info(f"{service_api}: 合并成功 -> {output_filename}")
            else:
                self._log_warning(f"{service_api}: 合并失败，保留分段文件")

        return results

    def _safe_merge_with_fallback(self, files: List[str], project_dir: str, output_path: str) -> bool:
        """安全合并，失败时保留原文件

        Args:
            files: 分段文件列表
            project_dir: 项目目录
            output_path: 输出路径

        Returns:
            bool: 是否成功
        """
        try:
            return self.merge_subtitle_group(files, project_dir, output_path)
        except Exception as e:
            self._log_error(f"合并过程异常: {e}")
            return False
